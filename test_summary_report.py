"""
Test script to verify the updated summary report
"""

import datetime
from collections import defaultdict

# Create sample earthquake data with depth
events = [
    {
        "time_local": datetime.datetime.now() - datetime.timedelta(days=5),
        "mag": 5.2,
        "lat": 21.9,
        "lon": 96.1,
        "depth": 10.5,
        "place_mm": "Test MM 5.2",
        "place_en": "Test EN 5.2"
    },
    {
        "time_local": datetime.datetime.now() - datetime.timedelta(days=3),
        "mag": 4.7,
        "lat": 22.0,
        "lon": 96.2,
        "depth": 15.3,
        "place_mm": "Test MM 4.7",
        "place_en": "Test EN 4.7"
    },
    {
        "time_local": datetime.datetime.now() - datetime.timedelta(days=2),
        "mag": 4.2,
        "lat": 22.1,
        "lon": 96.3,
        "depth": None,  # Test with None depth
        "place_mm": "Test MM 4.2",
        "place_en": "Test EN 4.2"
    },
    {
        "time_local": datetime.datetime.now() - datetime.timedelta(days=1),
        "mag": 3.8,
        "lat": 22.2,
        "lon": 96.4,
        "depth": 25.7,
        "place_mm": "Test MM 3.8",
        "place_en": "Test EN 3.8"
    },
    {
        "time_local": datetime.datetime.now() - datetime.timedelta(hours=12),
        "mag": 3.3,
        "lat": 22.3,
        "lon": 96.5,
        "depth": 8.2,
        "place_mm": "Test MM 3.3",
        "place_en": "Test EN 3.3"
    },
]

# Constants
MIN_MAG = 3.0
MANDALAY_LAT, MANDALAY_LON = 21.9759, 96.0845
SEARCH_RADIUS_KM = 400

# Find largest earthquake
largest_quake = max(events, key=lambda e: e["mag"])
largest_quake_date = largest_quake["time_local"].strftime("%d-%B-%Y")
largest_quake_place = largest_quake["place_en"]
largest_quake_depth = f"{largest_quake['depth']:.1f} km" if largest_quake["depth"] is not None else "Unknown depth"

# Calculate distance (simplified for test)
distance_to_largest = 150.5  # km

# Calculate average magnitude and depth
avg_magnitude = sum(e["mag"] for e in events) / len(events)

# Calculate average depth (excluding None values)
depths = [e["depth"] for e in events if e["depth"] is not None]
avg_depth = sum(depths) / len(depths) if depths else None

# Find deepest earthquake
deepest_quake = max([e for e in events if e["depth"] is not None], key=lambda e: e["depth"], default=None)
deepest_info = ""
if deepest_quake:
    deepest_info = f"Deepest earthquake: M{deepest_quake['mag']:.1f} at {deepest_quake['depth']:.1f} km near {deepest_quake['place_en']}"

# Count earthquakes by magnitude range
mag_counts = defaultdict(int)
total_counts = {
    "above_4.5": 0,
    "above_4.0": 0,
    "above_3.5": 0,
    "above_3.0": 0,
    "total": len(events)
}

# Get quakes above 4.5 for detailed listing
significant_quakes = []

for e in events:
    mag = e["mag"]
    if mag >= 7.5:
        mag_counts["7.5+"] += 1
        total_counts["above_4.5"] += 1
        significant_quakes.append(e)
    elif mag >= 7.0:
        mag_counts["7.0-7.5"] += 1
        total_counts["above_4.5"] += 1
        significant_quakes.append(e)
    elif mag >= 6.5:
        mag_counts["6.5-7.0"] += 1
        total_counts["above_4.5"] += 1
        significant_quakes.append(e)
    elif mag >= 6.0:
        mag_counts["6.0-6.5"] += 1
        total_counts["above_4.5"] += 1
        significant_quakes.append(e)
    elif mag >= 5.5:
        mag_counts["5.5-6.0"] += 1
        total_counts["above_4.5"] += 1
        significant_quakes.append(e)
    elif mag >= 5.0:
        mag_counts["5.0-5.5"] += 1
        total_counts["above_4.5"] += 1
        significant_quakes.append(e)
    elif mag >= 4.5:
        mag_counts["4.5-5.0"] += 1
        total_counts["above_4.5"] += 1
        significant_quakes.append(e)
    elif mag >= 4.0:
        mag_counts["4.0-4.5"] += 1
        total_counts["above_4.0"] += 1
    elif mag >= 3.5:
        mag_counts["3.5-4.0"] += 1
        total_counts["above_3.5"] += 1
    elif mag >= 3.0:
        mag_counts["3.0-3.5"] += 1
        total_counts["above_3.0"] += 1
    elif mag >= 2.0:
        mag_counts["2.0-3.0"] += 1

# Sort significant quakes by magnitude (descending)
significant_quakes.sort(key=lambda e: e["mag"], reverse=True)

# Create the report file
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
report_filename = f"Test_Summary_{timestamp}.txt"
with open(report_filename, "w", encoding="utf-8") as f:
    f.write(f"Earthquake around {SEARCH_RADIUS_KM} kilometer radius center at Mandalay ({MANDALAY_LAT}/{MANDALAY_LON})\n\n")

    f.write(f"Start Time: 2025-03-28T00:00:00\n")
    f.write(f"End Time: 2025-04-30T23:59:59\n\n")

    current_date = datetime.datetime.now()
    day_name = current_date.strftime("%A")
    date_formatted = current_date.strftime("%d-%B-%Y %H:%M:%S")
    f.write(f"Date report created: {day_name} {date_formatted}\n\n")

    f.write(f"Largest earthquake {largest_quake['mag']} quake at {largest_quake_date} near {largest_quake_place}\n")
    f.write(f"Depth of largest quake: {largest_quake_depth}\n")
    f.write(f"Distance between biggest quake and city: {distance_to_largest:.1f} km\n")
    f.write(f"Average quake: {avg_magnitude:.1f}\n")
    if avg_depth is not None:
        f.write(f"Average depth: {avg_depth:.1f} km\n")
    if deepest_quake:
        f.write(f"{deepest_info}\n")

    # Write total counts
    f.write(f"Total {total_counts['total']} earthquakes above {MIN_MAG}\n")
    f.write(f"Total {total_counts['above_4.5']} earthquakes above 4.5\n")
    f.write(f"Total {total_counts['above_4.0']} earthquakes above 4.0\n")
    f.write(f"Total {total_counts['above_3.5']} earthquakes above 3.5\n")
    f.write(f"Total {total_counts['above_3.0']} earthquakes above 3.0\n\n")

    # Write details of significant quakes (above 4.5)
    if significant_quakes:
        f.write("Significant Earthquakes (M ≥ 4.5)\n")
        f.write("================================\n")
        for i, quake in enumerate(significant_quakes, 1):
            depth_str = f"{quake['depth']:.1f} km" if quake["depth"] is not None else "Unknown depth"
            date_str = quake["time_local"].strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"{i}. M{quake['mag']:.1f} on {date_str}\n")
            f.write(f"   Location: {quake['place_en']}\n")
            if 'place_mm' in quake and quake['place_mm']:
                f.write(f"   Local name: {quake['place_mm']}\n")
            f.write(f"   Depth: {depth_str}\n")
            f.write(f"   Coordinates: {quake['lat']:.4f}, {quake['lon']:.4f}\n\n")
        f.write("\n")

    f.write("Quake list by Magnitude Range\n")
    f.write("============================\n")

    # Format the counts with appropriate wording
    for mag_range, count in sorted(mag_counts.items(), reverse=True):
        if count == 1:
            descriptor = "One"
        elif count == 2:
            descriptor = "Two"
        elif count == 3:
            descriptor = "Three"
        else:
            descriptor = str(count)

        if mag_range == "7.5+":
            f.write(f"{descriptor} {mag_range} ({count})\n")
        elif mag_range == "7.0-7.5":
            f.write(f"{descriptor} between 7.0 and 7.5 ({count})\n")
        elif mag_range == "6.5-7.0":
            f.write(f"{descriptor} between 6.5 and 7.0 ({count})\n")
        elif mag_range == "6.0-6.5":
            f.write(f"{descriptor} between 6.0 and 6.5 ({count})\n")
        elif mag_range == "5.5-6.0":
            f.write(f"{descriptor} between 5.5 and 6.0 ({count})\n")
        elif mag_range == "5.0-5.5":
            f.write(f"{descriptor} between 5.0 and 5.5 ({count})\n")
        elif mag_range == "4.5-5.0":
            f.write(f"{descriptor} between 4.5 and 5.0 ({count})\n")
        elif mag_range == "4.0-4.5":
            f.write(f"{descriptor} between 4.0 and 4.5 ({count})\n")
        elif mag_range == "3.5-4.0":
            f.write(f"{descriptor} between 3.5 and 4.0 ({count})\n")
        elif mag_range == "3.0-3.5":
            f.write(f"{descriptor} between 3.0 and 3.5 ({count})\n")
        elif mag_range == "2.0-3.0":
            f.write(f"{descriptor} between 2.0 and 3.0 ({count})\n")

print(f"Test summary report saved to {report_filename}")
print("\nSummary of test data:")
print(f"Total earthquakes: {total_counts['total']}")
print(f"Earthquakes above 4.5: {total_counts['above_4.5']}")
print(f"Significant quakes: {len(significant_quakes)}")
for i, quake in enumerate(significant_quakes, 1):
    print(f"{i}. M{quake['mag']:.1f} at {quake['place_en']}")
