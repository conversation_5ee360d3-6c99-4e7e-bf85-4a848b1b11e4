import matplotlib.pyplot as plt
import numpy as np

# Building specs
height_ft = 80  # 8 floors x 10 ft

# Two sway scenarios: 2% and 3%
sway_percentages = [0.01, 0.02, 0.03, 0.07, 0.12]
sways = [height_ft * p for p in sway_percentages]
angles_deg = [np.degrees(np.arctan(s / height_ft)) for s in sways]

# Create diagram
fig, ax = plt.subplots()
ax.plot([0, 0], [0, height_ft], label="Original (No Tilt)", color='gray', linestyle='--')

colors = ['green', 'blue', 'red', 'brown', 'black']
for sway, angle, color, percent in zip(sways, angles_deg, colors, sway_percentages):
    ax.plot([0, sway], [0, height_ft], label=f"{int(percent*100)}% Sway (~{angle:.2f}°)", color=color)
    ax.text(sway, height_ft -21 + sways.index(sway) * 2, f"{sway:.2f} ft", color=color)

ax.set_xlabel("Horizontal Slip (ft)")
ax.set_ylabel("Building Height (ft)")
ax.set_title("Building Tilt During Earthquake")
ax.legend()
ax.set_aspect('equal')
ax.grid(True)

plt.tight_layout()
plt.show()
