import requests
import json
import csv
import os
import folium
from datetime import datetime, timedelta

def get_earthquake_data(start_date, end_date, min_lat, max_lat, min_lon, max_lon):
    # USGS Earthquake API URL
    url = "https://earthquake.usgs.gov/fdsnws/event/1/query"

    # API Parameters
    params = {
        "format": "geojson",
        "starttime": start_date,
        "endtime": end_date,
        #"minlatitude": min_lat,
        #"maxlatitude": max_lat,
        #"minlongitude": min_lon,
        #"maxlongitude": max_lon,
        "minmagnitude": 2.5,  # Set to 2.5 to get more data
        "limit": 20000,  # Maximum number of results
        "orderby": "time"  
    }

    try:
        # Send GET request to USGS API
        response = requests.get(url, params=params)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        # Parse JSON response
        data = response.json()
        
        
        # Check if any earthquakes were found
        if not data["features"]:
            print("No earthquake data found in the given period and location.")
            return
        
        # Get script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Generate timestamp for filenames
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save earthquake data to CSV file
        csv_filename = os.path.join(script_dir, f"MyanmarQuakeInfo_{timestamp}.csv")
        with open(csv_filename, mode="w", newline="", encoding="utf-8") as file:
            writer = csv.writer(file)
            writer.writerow(["Datetime (MMT)", "Magnitude", "Latitude", "Longitude", "Place", "Depth (km)"])
            
            for quake in data["features"]:
                quake_time_utc = datetime.utcfromtimestamp(quake["properties"]["time"] / 1000)
                quake_time_mmt = quake_time_utc + timedelta(hours=6, minutes=30)  # Convert to Myanmar Time
                magnitude = quake["properties"]["mag"]
                latitude = quake["geometry"]["coordinates"][1]
                longitude = quake["geometry"]["coordinates"][0]
                place = quake["properties"]["place"]
                depth = quake["geometry"]["coordinates"][2]

                writer.writerow([quake_time_mmt, magnitude, latitude, longitude, place, depth])
        
        print(f"Earthquake data saved to: {csv_filename}")

        # Create Myanmar Map using Folium
        myanmar_map = folium.Map(location=[21.0, 96.0], zoom_start=6)

        # Add earthquake markers to the map
        for quake in data["features"]:
            quake_time_utc = datetime.utcfromtimestamp(quake["properties"]["time"] / 1000)
            quake_time_mmt = quake_time_utc + timedelta(hours=6, minutes=30)  # Convert to Myanmar Time
            magnitude = quake["properties"]["mag"]
            latitude = quake["geometry"]["coordinates"][1]
            longitude = quake["geometry"]["coordinates"][0]
            place = quake["properties"]["place"]

            # Create tooltip message
            tooltip_text = f"""
                <b>Location:</b> {place} <br>
                <b>Magnitude:</b> {magnitude} <br>
                <b>Datetime:</b> {quake_time_mmt.strftime('%Y-%m-%d %H:%M:%S')}
            """

            # Add marker with tooltip
            folium.Marker(
                location=[latitude, longitude],
                popup=tooltip_text,
                tooltip="Click for details",
                icon=folium.Icon(color="red", icon="info-sign")
            ).add_to(myanmar_map)

        # Save the map to an HTML file
        map_filename = os.path.join(script_dir, f"MyanmarEarthquakeMap_{timestamp}.html")
        myanmar_map.save(map_filename)
        print(f"Earthquake map saved to: {map_filename}")

    except requests.exceptions.RequestException as e:
        print(f"Error fetching data: {e}")

# Example usage: Get earthquakes in Myanmar for the last 30 days
start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
end_date = datetime.now().strftime("%Y-%m-%d")

# Myanmar bounding box (Approximate)
min_lat, max_lat = 9.5, 28.5
min_lon, max_lon = 92.0, 101.0

get_earthquake_data(start_date, end_date, min_lat, max_lat, min_lon, max_lon)
