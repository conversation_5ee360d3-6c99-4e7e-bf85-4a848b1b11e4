import folium
import webbrowser
import os

# Coordinates for Mandalay, Myanmar
mandalay_coords = (21.95, 96.08)

# Create a Folium map centered at Mandalay
m = folium.Map(location=mandalay_coords, zoom_start=12, width=1600, height=1200)

# Add zoom and pan controls (they are enabled by default)

# Save the map as an HTML file
filepath = "mandalay_map.html"
m.save(filepath)

# Open the HTML file in the default web browser
webbrowser.open("file://" + os.path.realpath(filepath))

print(f"Map saved to {filepath} and opened in your default browser.")