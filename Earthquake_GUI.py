"""
Earthquake_GUI.py
-------------------------------------------------------------
Modern GUI interface for earthquake data retrieval and visualization
 - Date range, magnitude, and search radius filters
 - Summary display and earthquake list
 - Generates CSV, KML, and plot files
-------------------------------------------------------------
"""

import sys
import os
import requests
import math
import datetime
import csv
from collections import Counter, defaultdict
from zoneinfo import ZoneInfo
import matplotlib.pyplot as plt
from zipfile import ZipFile
import simplekml
import time
import unicodedata
import re
from urllib.parse import urlencode
import numpy as np
from matplotlib.dates import DateFormatter, HourLocator
from PyQt5.QtWidgets import (QApplication, Q<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, QVBoxLayout, QHBox<PERSON>ayout,
                            QL<PERSON>l, QPushButton, QComboBox, QLineEdit, QCalendarWidget,
                            QTextEdit, QTableWidget, QTableWidgetItem, QHeaderView,
                            QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox, QTabWidget,
                            QFileDialog, QMessageBox, QProgressBar, QSplitter, QDateEdit,
                            QCheckBox, QRadioButton, QGridLayout, QDialog)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap, QKeyEvent

# Default settings
DEFAULT_START_DATE = "2025-03-28"
DEFAULT_MIN_MAG = 4.5
DEFAULT_SEARCH_RADIUS_KM = 400
LOCAL_TZ = ZoneInfo("Asia/Yangon")

# Predefined cities
PREDEFINED_CITIES = {
    "Mandalay": (21.9759, 96.0845),
    "Yangon": (16.8661, 96.1951),
    "Bangkok": (13.7563, 100.5018),
    "Singapore": (1.3521, 103.8198),
    "Tokyo": (35.6762, 139.6503),
    "Beijing": (39.9042, 116.4074),
    "New Delhi": (28.6139, 77.2090),
    "Jakarta": (6.2088, 106.8456)
}

# Try to import unidecode for romanization
try:
    from unidecode import unidecode
    romanise = lambda s: unidecode(s)
except ImportError:
    romanise = lambda s: unicodedata.normalize("NFKD", s).encode("ascii","ignore").decode()

class EarthquakeWorker(QThread):
    """Worker thread to fetch and process earthquake data"""
    progress_update = pyqtSignal(str)
    data_ready = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    status_changed = pyqtSignal(str)  # Signal for worker status changes

    def __init__(self, start_date, end_date, min_mag, search_params):
        super().__init__()
        self.start_date = start_date
        self.end_date = end_date
        self.min_mag = min_mag
        self.search_params = search_params

        # State variables for pause/stop
        self.is_paused = False
        self.is_stopped = False
        self.processed_events = []  # Store partially processed events

    def run(self):
        try:
            self.progress_update.emit("Fetching earthquake data...")
            events = self.fetch_earthquake_data()

            if not events:
                self.error_occurred.emit("No matching earthquakes found.")
                return

            # Check if stopped during fetch
            if self.is_stopped:
                self.status_changed.emit("stopped")
                return

            self.progress_update.emit("Processing earthquake data...")
            self.process_earthquake_data(events)

            # Only emit data_ready if not stopped
            if not self.is_stopped:
                self.data_ready.emit(events)
            else:
                self.status_changed.emit("stopped")

        except Exception as e:
            self.error_occurred.emit(f"Error: {str(e)}")

    def pause(self):
        """Pause the worker thread"""
        self.is_paused = True
        self.status_changed.emit("paused")

    def resume(self):
        """Resume the worker thread"""
        self.is_paused = False
        self.status_changed.emit("running")

    def stop(self):
        """Stop the worker thread"""
        self.is_stopped = True
        self.is_paused = False
        # If we have partial results, emit them
        if self.processed_events:
            self.data_ready.emit(self.processed_events)
        self.status_changed.emit("stopped")

    def fetch_earthquake_data(self):
        """Fetch earthquake data from EMSC"""
        # Define geographic boundaries based on search method
        if self.search_params["method"] == "rectangle":
            minlatitude = self.search_params["min_lat"]
            maxlatitude = self.search_params["max_lat"]
            minlongitude = self.search_params["min_lon"]
            maxlongitude = self.search_params["max_lon"]
        else:  # circle method - use a bounding box that encompasses the circle
            # Calculate a bounding box that encompasses the circle
            center_lat = self.search_params["center_lat"]
            center_lon = self.search_params["center_lon"]
            radius_km = self.search_params["radius"]

            # Approximate degrees per km (varies by latitude)
            lat_km = 1/111.0  # roughly 111 km per degree of latitude
            lon_km = 1/(111.0 * math.cos(math.radians(center_lat)))  # longitude degrees per km

            # Calculate bounding box with some padding
            lat_delta = (radius_km * lat_km) * 1.2  # Add 20% padding
            lon_delta = (radius_km * lon_km) * 1.2

            minlatitude = center_lat - lat_delta
            maxlatitude = center_lat + lat_delta
            minlongitude = center_lon - lon_delta
            maxlongitude = center_lon + lon_delta

            # Ensure values are within valid ranges
            minlatitude = max(-90, minlatitude)
            maxlatitude = min(90, maxlatitude)
            minlongitude = max(-180, minlongitude)
            maxlongitude = min(180, maxlongitude)

        # Build EMSC URL with bounding box
        url = (
            "https://www.seismicportal.eu/fdsnws/event/1/query"
            "?format=json"
            f"&starttime={self.start_date}T00:00:00"
            f"&endtime={self.end_date}T23:59:59"
            f"&minlatitude={minlatitude}"
            f"&minlongitude={minlongitude}"
            f"&maxlatitude={maxlatitude}"
            f"&maxlongitude={maxlongitude}"
            f"&minmag={self.min_mag}"
        )

        self.progress_update.emit(f"Requesting data from EMSC...")

        try:
            r = requests.get(url, timeout=60)
            r.raise_for_status()
            j = r.json()

            if "features" in j:  # GeoJSON
                src = j["features"]
                to_iso = lambda ev: ev["properties"]["time"]
                to_mag = lambda ev: ev["properties"]["mag"]
                to_lat = lambda ev: ev["geometry"]["coordinates"][1]
                to_lon = lambda ev: ev["geometry"]["coordinates"][0]
                to_depth = lambda ev: ev["geometry"]["coordinates"][2] if len(ev["geometry"]["coordinates"]) > 2 else None
                to_place = lambda ev: ev["properties"].get("flynn_region", "")
            else:  # flat list
                src = j.get("events", [])
                to_iso = lambda e: e["time"]
                to_mag = lambda e: e["mag"]
                to_lat = lambda e: e["lat"]
                to_lon = lambda e: e["lon"]
                to_depth = lambda e: e.get("depth", None)
                to_place = lambda e: e.get("flynn_region", "")

            events = []
            for ev in src:
                mag = to_mag(ev)
                if mag is None or mag < self.min_mag:  # Skip events without magnitude or below threshold
                    continue

                lat, lon = to_lat(ev), to_lon(ev)

                # Filter based on search method
                if self.search_params["method"] == "circle":
                    center_lat = self.search_params["center_lat"]
                    center_lon = self.search_params["center_lon"]
                    radius = self.search_params["radius"]

                    if self.haversine_km(center_lat, center_lon, lat, lon) > radius:
                        continue
                else:
                    # Rectangle method - already filtered by the API query bounding box
                    pass

                t_utc = datetime.datetime.fromisoformat(to_iso(ev).replace("Z", "+00:00"))
                t_loc = t_utc.astimezone(LOCAL_TZ)
                depth = to_depth(ev)

                # Convert depth to km if it's not None (some APIs provide depth in meters)
                if depth is not None and depth > 1000:
                    depth = depth / 1000

                events.append({
                    "time_local": t_loc,
                    "mag": mag,
                    "lat": lat,
                    "lon": lon,
                    "depth": depth,
                    "place": to_place(ev),
                    "source": "EMSC",
                    "id": ev.get("id", "") if "id" in ev else ev.get("properties", {}).get("id", "")
                })

            self.progress_update.emit(f"Retrieved {len(events)} earthquakes from EMSC")
            return events

        except Exception as e:
            self.error_occurred.emit(f"Error fetching EMSC data: {str(e)}")
            return []

    def process_earthquake_data(self, events):
        """Process earthquake data with reverse geocoding"""
        cache = {}  # (lat,lon,lang) → address dict

        priority_fine = ["village", "quarter", "municipality", "county"]
        priority_town = ["quarter", "township", "city", "town", "county", "district", "state"]

        self.progress_update.emit("Reverse-geocoding locations...")

        # Start with any previously processed events
        if not self.processed_events:
            self.processed_events = []

        # Process only events that haven't been processed yet
        events_to_process = [e for e in events if e not in self.processed_events]

        for idx, e in enumerate(events_to_process):
            # Check if paused
            while self.is_paused and not self.is_stopped:
                time.sleep(0.1)  # Sleep briefly to avoid CPU spinning
                continue

            # Check if stopped
            if self.is_stopped:
                return

            total_processed = len(self.processed_events) + idx + 1
            total_events = len(self.processed_events) + len(events_to_process)
            self.progress_update.emit(f"Processing location {total_processed}/{total_events}...")

            # Reverse geocode to get place names
            mm_addr = self.nominatim(e["lat"], e["lon"], "my", cache).get("address", {})
            en_addr = self.nominatim(e["lat"], e["lon"], "en", cache).get("address", {})

            # ---- Burmese name (fine level or fallback) -------------------
            place_mm = "unknown"
            for k in priority_fine + priority_town:
                if k in mm_addr:
                    place_mm = mm_addr[k].strip()
                    break

            # ---- English name prioritizing municipality and county over state ---------------------
            place_en = "Unknown"  # Default value

            # First try municipality directly
            if 'municipality' in en_addr and en_addr['municipality'].strip():
                place_en = en_addr['municipality'].strip()
                if 'county' in en_addr and en_addr['county'].strip():
                    place_en += f", {en_addr['county']}"
            # Then try county if municipality not available
            elif 'county' in en_addr and en_addr['county'].strip():
                place_en = en_addr['county'].strip()
            # Then try district if neither municipality nor county available
            elif 'district' in en_addr and en_addr['district'].strip():
                place_en = en_addr['district'].strip()
            # Then try detailed location with municipality/county context
            else:
                for k in priority_fine:
                    if k in en_addr and en_addr[k].strip():
                        place_en = en_addr[k].strip()
                        # Add municipality or county name for better context
                        if 'municipality' in en_addr:
                            place_en += f", {en_addr['municipality']}"
                        elif 'county' in en_addr:
                            place_en += f", {en_addr['county']}"
                        break

            # If still no English name or if it contains Burmese characters, use state as fallback
            if place_en == "Unknown":
                # Always end with state check
                if 'state' in en_addr:
                    place_en = f"Near {en_addr['state']}"
                else:
                    # Use romanized Burmese name as last resort
                    place_en = romanise(place_mm)

            e["place_mm"] = place_mm
            e["place_en"] = place_en

            # Add to processed events
            self.processed_events.append(e)

            # Emit partial results every 5 events
            if idx % 5 == 0 and self.processed_events:
                self.progress_update.emit(f"Processed {len(self.processed_events)} events so far...")
                # We don't emit data_ready here to avoid UI updates during processing

    def nominatim(self, lat, lon, lang, cache):
        """Query Nominatim reverse geocoding service"""
        key = (round(lat, 4), round(lon, 4), lang)
        if key in cache:
            return cache[key]

        params = dict(format="jsonv2", lat=lat, lon=lon,
                      zoom=15, addressdetails=1,
                      accept_language=lang)
        url = "https://nominatim.openstreetmap.org/reverse?" + urlencode(params)
        headers = {"User-Agent": "Mandalay-Quake-Script/0.6 (contact: <EMAIL>)"}

        try:
            r = requests.get(url, headers=headers, timeout=10)
            data = r.json() if r.ok else {}
        except requests.RequestException:
            data = {}

        cache[key] = data
        time.sleep(1)  # respect 1 req/sec
        return data

    def haversine_km(self, lat1, lon1, lat2, lon2):
        """Calculate distance between two points in km"""
        R = 6371.0
        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)
        a = math.sin(dlat/2)**2 + math.cos(math.radians(lat1))*math.cos(math.radians(lat2))*math.sin(dlon/2)**2
        return 2*R*math.asin(math.sqrt(a))

class EarthquakeApp(QMainWindow):
    """Main application window"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Earthquake Data Viewer")
        self.setMinimumSize(900, 700)

        # Initialize data
        self.events = []

        # Default to Mandalay as focus city
        self.focus_city = "Mandalay"
        self.focus_lat, self.focus_lon = PREDEFINED_CITIES["Mandalay"]

        # Default to circle search
        self.search_method = "circle"

        # Worker thread reference
        self.worker = None

        # Processing state
        self.processing_state = "idle"  # idle, running, paused, stopped

        # Set up the UI
        self.setup_ui()

        # Enable key press events
        self.setFocusPolicy(Qt.StrongFocus)

    def keyPressEvent(self, event):
        """Handle key press events"""
        # Check for ESC key
        if event.key() == Qt.Key_Escape:
            if self.processing_state == "running":
                self.pause_processing()
            elif self.processing_state == "paused":
                self.show_pause_dialog()

        # Pass event to parent class
        super().keyPressEvent(event)

    def setup_ui(self):
        """Set up the user interface"""
        # Main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)

        # Input parameters section
        input_group = QGroupBox("Search Parameters")
        input_layout = QFormLayout()

        # Date range selection
        date_layout = QHBoxLayout()

        self.start_date_label = QLabel("Start Date:")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.fromString(DEFAULT_START_DATE, "yyyy-MM-dd"))

        self.end_date_label = QLabel("End Date:")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())

        date_layout.addWidget(self.start_date_label)
        date_layout.addWidget(self.start_date_edit)
        date_layout.addWidget(self.end_date_label)
        date_layout.addWidget(self.end_date_edit)

        # Focus city selection
        city_layout = QHBoxLayout()

        self.city_label = QLabel("Focus City:")
        self.city_combo = QComboBox()
        for city in sorted(PREDEFINED_CITIES.keys()):
            self.city_combo.addItem(city)

        # Set default to Mandalay
        self.city_combo.setCurrentText("Mandalay")
        self.city_combo.currentTextChanged.connect(self.on_city_changed)

        # Custom coordinates option
        self.custom_coords_button = QPushButton("Custom Coordinates...")
        self.custom_coords_button.clicked.connect(self.show_custom_coords_dialog)

        city_layout.addWidget(self.city_label)
        city_layout.addWidget(self.city_combo)
        city_layout.addWidget(self.custom_coords_button)

        # Search region options
        region_group = QGroupBox("Search Region")
        region_layout = QVBoxLayout()

        # Radio buttons for search method
        self.circle_radio = QRadioButton("Circle (center and radius)")
        self.rectangle_radio = QRadioButton("Rectangle (min/max coordinates)")

        # Set default to circle
        self.circle_radio.setChecked(True)

        # Connect signals
        self.circle_radio.toggled.connect(self.on_search_method_changed)
        self.rectangle_radio.toggled.connect(self.on_search_method_changed)

        # Add radio buttons to layout
        method_layout = QHBoxLayout()
        method_layout.addWidget(self.circle_radio)
        method_layout.addWidget(self.rectangle_radio)
        region_layout.addLayout(method_layout)

        # Circle search parameters (default)
        self.circle_widget = QWidget()
        circle_layout = QHBoxLayout(self.circle_widget)

        self.radius_label = QLabel("Search Radius (km):")
        self.radius_spin = QSpinBox()
        self.radius_spin.setRange(100, 1000)
        self.radius_spin.setValue(DEFAULT_SEARCH_RADIUS_KM)
        self.radius_spin.setSingleStep(50)

        circle_layout.addWidget(self.radius_label)
        circle_layout.addWidget(self.radius_spin)
        circle_layout.addStretch()

        # Rectangle search parameters (hidden initially)
        self.rectangle_widget = QWidget()
        rectangle_layout = QGridLayout(self.rectangle_widget)

        self.min_lat_label = QLabel("Min Latitude:")
        self.min_lat_spin = QDoubleSpinBox()
        self.min_lat_spin.setRange(-90, 90)
        self.min_lat_spin.setDecimals(4)
        self.min_lat_spin.setValue(16.5)  # Default value

        self.max_lat_label = QLabel("Max Latitude:")
        self.max_lat_spin = QDoubleSpinBox()
        self.max_lat_spin.setRange(-90, 90)
        self.max_lat_spin.setDecimals(4)
        self.max_lat_spin.setValue(23.7)  # Default value

        self.min_lon_label = QLabel("Min Longitude:")
        self.min_lon_spin = QDoubleSpinBox()
        self.min_lon_spin.setRange(-180, 180)
        self.min_lon_spin.setDecimals(4)
        self.min_lon_spin.setValue(95.0)  # Default value

        self.max_lon_label = QLabel("Max Longitude:")
        self.max_lon_spin = QDoubleSpinBox()
        self.max_lon_spin.setRange(-180, 180)
        self.max_lon_spin.setDecimals(4)
        self.max_lon_spin.setValue(96.8)  # Default value

        rectangle_layout.addWidget(self.min_lat_label, 0, 0)
        rectangle_layout.addWidget(self.min_lat_spin, 0, 1)
        rectangle_layout.addWidget(self.max_lat_label, 0, 2)
        rectangle_layout.addWidget(self.max_lat_spin, 0, 3)
        rectangle_layout.addWidget(self.min_lon_label, 1, 0)
        rectangle_layout.addWidget(self.min_lon_spin, 1, 1)
        rectangle_layout.addWidget(self.max_lon_label, 1, 2)
        rectangle_layout.addWidget(self.max_lon_spin, 1, 3)

        # Hide rectangle widget initially
        self.rectangle_widget.hide()

        # Add widgets to region layout
        region_layout.addWidget(self.circle_widget)
        region_layout.addWidget(self.rectangle_widget)

        region_group.setLayout(region_layout)

        # Magnitude and radius on same line
        mag_radius_layout = QHBoxLayout()

        # Magnitude filter
        self.mag_label = QLabel("Minimum Magnitude:")
        self.mag_combo = QComboBox()
        for mag in [round(m * 0.1, 1) for m in range(25, 56)]:  # 2.5 to 5.5 with 0.1 steps
            self.mag_combo.addItem(str(mag))
        # Set default to 4.5
        self.mag_combo.setCurrentText(str(DEFAULT_MIN_MAG))

        # Search radius
        self.radius_label = QLabel("Search Radius (km):")
        self.radius_spin = QSpinBox()
        self.radius_spin.setRange(100, 1000)
        self.radius_spin.setValue(DEFAULT_SEARCH_RADIUS_KM)
        self.radius_spin.setSingleStep(50)

        # Add to horizontal layout
        mag_radius_layout.addWidget(self.mag_label)
        mag_radius_layout.addWidget(self.mag_combo)
        mag_radius_layout.addSpacing(20)  # Add some space between the controls
        mag_radius_layout.addWidget(self.radius_label)
        mag_radius_layout.addWidget(self.radius_spin)

        # Output options
        options_group = QGroupBox("Output Options")
        options_layout = QVBoxLayout()

        # Create checkboxes for output options
        self.summary_checkbox = QCheckBox("Earthquake Summary")
        self.kmz_checkbox = QCheckBox("KMZ File Output")
        self.csv_checkbox = QCheckBox("CSV File Output")
        self.daily_counts_checkbox = QCheckBox("Daily Counts Plot")
        self.magnitude_freq_checkbox = QCheckBox("Magnitude Frequency Plot")

        # Set default states (only KMZ checked by default)
        self.summary_checkbox.setChecked(False)
        self.kmz_checkbox.setChecked(True)
        self.csv_checkbox.setChecked(False)
        self.daily_counts_checkbox.setChecked(False)
        self.magnitude_freq_checkbox.setChecked(False)

        # Add checkboxes to options layout
        options_layout.addWidget(self.summary_checkbox)
        options_layout.addWidget(self.kmz_checkbox)
        options_layout.addWidget(self.csv_checkbox)
        options_layout.addWidget(self.daily_counts_checkbox)
        options_layout.addWidget(self.magnitude_freq_checkbox)

        options_group.setLayout(options_layout)

        # Add widgets to form layout
        input_layout.addRow("Date Range:", date_layout)
        input_layout.addRow("Focus City:", city_layout)
        input_layout.addRow(region_group)
        input_layout.addRow(mag_radius_layout)
        input_layout.addRow(options_group)

        input_group.setLayout(input_layout)

        # Run button
        self.run_button = QPushButton("Fetch Earthquake Data")
        self.run_button.clicked.connect(self.fetch_data)

        # Process control buttons
        self.process_controls = QHBoxLayout()

        self.pause_button = QPushButton("Pause (ESC)")
        self.pause_button.clicked.connect(self.pause_processing)
        self.pause_button.setEnabled(False)

        self.resume_button = QPushButton("Resume")
        self.resume_button.clicked.connect(self.resume_processing)
        self.resume_button.setEnabled(False)

        self.stop_button = QPushButton("Stop")
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)

        self.process_controls.addWidget(self.pause_button)
        self.process_controls.addWidget(self.resume_button)
        self.process_controls.addWidget(self.stop_button)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.progress_bar.hide()

        # Status label
        self.status_label = QLabel("Ready")

        # Tab widget for results
        self.results_tabs = QTabWidget()

        # Summary tab
        self.summary_tab = QWidget()
        summary_layout = QVBoxLayout(self.summary_tab)
        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        summary_layout.addWidget(self.summary_text)

        # Earthquake list tab
        self.list_tab = QWidget()
        list_layout = QVBoxLayout(self.list_tab)
        self.quake_table = QTableWidget()
        self.quake_table.setColumnCount(7)
        self.quake_table.setHorizontalHeaderLabels(["Date/Time", "Magnitude", "Depth (km)",
                                                   "Location", "Latitude", "Longitude", "ID"])
        self.quake_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        list_layout.addWidget(self.quake_table)

        # Add tabs
        self.results_tabs.addTab(self.summary_tab, "Summary")
        self.results_tabs.addTab(self.list_tab, "Earthquake List")

        # Add widgets to main layout
        main_layout.addWidget(input_group)
        main_layout.addWidget(self.run_button)

        # Add process control buttons
        process_controls_widget = QWidget()
        process_controls_widget.setLayout(self.process_controls)
        main_layout.addWidget(process_controls_widget)

        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(self.status_label)
        main_layout.addWidget(self.results_tabs)

        # Set the main widget
        self.setCentralWidget(main_widget)

    def fetch_data(self):
        """Fetch earthquake data based on input parameters"""
        # Get input parameters
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
        min_mag = float(self.mag_combo.currentText())

        # Get search parameters based on selected method
        if self.search_method == "circle":
            search_radius = self.radius_spin.value()
            search_params = {
                "method": "circle",
                "center_lat": self.focus_lat,
                "center_lon": self.focus_lon,
                "radius": search_radius,
                "focus_city": self.focus_city
            }
        else:  # rectangle
            search_params = {
                "method": "rectangle",
                "min_lat": self.min_lat_spin.value(),
                "max_lat": self.max_lat_spin.value(),
                "min_lon": self.min_lon_spin.value(),
                "max_lon": self.max_lon_spin.value(),
                "focus_city": self.focus_city
            }

        # Show progress
        self.progress_bar.show()
        self.status_label.setText("Fetching data...")

        # Update UI state
        self.run_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.resume_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.processing_state = "running"

        # Create worker thread
        self.worker = EarthquakeWorker(start_date, end_date, min_mag, search_params)
        self.worker.progress_update.connect(self.update_status)
        self.worker.data_ready.connect(self.display_results)
        self.worker.error_occurred.connect(self.show_error)
        self.worker.status_changed.connect(self.on_worker_status_changed)
        self.worker.finished.connect(self.process_complete)

        # Start worker
        self.worker.start()

    def pause_processing(self):
        """Pause the processing"""
        if self.worker and self.processing_state == "running":
            self.worker.pause()
            self.processing_state = "paused"
            self.status_label.setText("Processing paused. Press ESC to continue or stop.")
            self.pause_button.setEnabled(False)
            self.resume_button.setEnabled(True)

            # Show partial results in the UI
            if self.worker.processed_events:
                self.status_label.setText(f"Processing paused. Showing {len(self.worker.processed_events)} events processed so far.")
                self.display_partial_results(self.worker.processed_events)

    def resume_processing(self):
        """Resume the processing"""
        if self.worker and self.processing_state == "paused":
            self.worker.resume()
            self.processing_state = "running"
            self.status_label.setText("Processing resumed...")
            self.pause_button.setEnabled(True)
            self.resume_button.setEnabled(False)

    def stop_processing(self):
        """Stop the processing"""
        if self.worker and (self.processing_state == "running" or self.processing_state == "paused"):
            self.worker.stop()
            self.processing_state = "stopped"
            self.status_label.setText("Processing stopped. Partial results may be available.")

    def show_pause_dialog(self):
        """Show dialog when ESC is pressed during pause"""
        if self.processing_state != "paused":
            return

        dialog = QMessageBox(self)
        dialog.setWindowTitle("Processing Paused")
        dialog.setText("Processing is currently paused.")
        dialog.setInformativeText("What would you like to do?")

        resume_button = dialog.addButton("Resume", QMessageBox.AcceptRole)
        stop_button = dialog.addButton("Stop", QMessageBox.RejectRole)

        dialog.exec_()

        if dialog.clickedButton() == resume_button:
            self.resume_processing()
        elif dialog.clickedButton() == stop_button:
            self.stop_processing()

    def on_worker_status_changed(self, status):
        """Handle worker status changes"""
        self.processing_state = status

        if status == "paused":
            self.pause_button.setEnabled(False)
            self.resume_button.setEnabled(True)
            self.status_label.setText("Processing paused. Press ESC to continue or stop.")

            # Show partial results in the UI
            if self.worker and self.worker.processed_events:
                self.status_label.setText(f"Processing paused. Showing {len(self.worker.processed_events)} events processed so far.")
                self.display_partial_results(self.worker.processed_events)

        elif status == "running":
            self.pause_button.setEnabled(True)
            self.resume_button.setEnabled(False)
            self.status_label.setText("Processing...")
        elif status == "stopped":
            self.pause_button.setEnabled(False)
            self.resume_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.run_button.setEnabled(True)

            # Show partial results in the UI
            if self.worker and self.worker.processed_events:
                self.status_label.setText(f"Processing stopped. Showing {len(self.worker.processed_events)} events processed so far.")
                self.display_partial_results(self.worker.processed_events)
            else:
                self.status_label.setText("Processing stopped. No results available.")

    def update_status(self, message):
        """Update status message"""
        self.status_label.setText(message)

    def display_partial_results(self, events):
        """Display partial results in the UI"""
        if not events:
            return

        # Store the events
        self.events = events

        # Update the summary tab with partial results
        self.update_summary(is_partial=True)

        # Update the earthquake list tab with partial results
        self.update_quake_list(is_partial=True)

        # Switch to the summary tab to show the partial results
        self.results_tabs.setCurrentIndex(0)

    def display_results(self, events):
        """Display results in the UI"""
        self.events = events

        # Update summary tab
        self.update_summary()

        # Update earthquake list tab
        self.update_quake_list()

        # Generate output files
        self.generate_output_files()

    def update_summary(self, is_partial=False):
        """Update the summary information"""
        if not self.events:
            self.summary_text.setText("No earthquake data available.")
            return

        # Calculate summary statistics
        total_events = len(self.events)
        largest_quake = max(self.events, key=lambda e: e["mag"])
        avg_magnitude = sum(e["mag"] for e in self.events) / total_events

        # Count earthquakes by magnitude range
        mag_counts = defaultdict(int)
        significant_quakes = []  # Quakes with magnitude >= 5.0

        for e in self.events:
            mag = e["mag"]
            if mag >= 7.5:
                mag_counts["7.5+"] += 1
                significant_quakes.append(e)
            elif mag >= 7.0:
                mag_counts["7.0-7.5"] += 1
                significant_quakes.append(e)
            elif mag >= 6.5:
                mag_counts["6.5-7.0"] += 1
                significant_quakes.append(e)
            elif mag >= 6.0:
                mag_counts["6.0-6.5"] += 1
                significant_quakes.append(e)
            elif mag >= 5.5:
                mag_counts["5.5-6.0"] += 1
                significant_quakes.append(e)
            elif mag >= 5.0:
                mag_counts["5.0-5.5"] += 1
                significant_quakes.append(e)
            elif mag >= 4.5:
                mag_counts["4.5-5.0"] += 1
            elif mag >= 4.0:
                mag_counts["4.0-4.5"] += 1
            elif mag >= 3.5:
                mag_counts["3.5-4.0"] += 1
            elif mag >= 3.0:
                mag_counts["3.0-3.5"] += 1

        # Sort significant quakes by magnitude (descending)
        significant_quakes.sort(key=lambda e: e["mag"], reverse=True)

        # Format summary text
        start_date = self.start_date_edit.date().toString("dd-MMMM-yyyy")
        end_date = self.end_date_edit.date().toString("dd-MMMM-yyyy")

        # Add partial results indicator if needed
        partial_indicator = " (PARTIAL RESULTS)" if is_partial else ""

        summary = f"""EARTHQUAKE SUMMARY{partial_indicator}
=================
Data source: EMSC
Date range: {start_date} to {end_date}
Total events: {total_events} earthquakes{" (processing incomplete)" if is_partial else ""}

Largest: M{largest_quake['mag']:.1f} on {largest_quake['time_local'].strftime('%d-%B-%Y')} near {largest_quake['place_en']}
Average magnitude: {avg_magnitude:.1f}

Magnitude distribution:
"""

        # Add magnitude distribution
        for mag_range, count in sorted(mag_counts.items(), reverse=True):
            if count > 0:  # Only include ranges with earthquakes
                summary += f"- {mag_range}: {count} earthquakes\n"

        # Add significant earthquakes (M5.0+) details
        if significant_quakes:
            summary += f"\nSignificant Earthquakes (M ≥ 5.0):\n"
            for i, quake in enumerate(significant_quakes, 1):
                depth_str = f"{quake['depth']:.1f} km" if quake["depth"] is not None else "Unknown depth"
                date_str = quake["time_local"].strftime("%Y-%m-%d")
                summary += f"{i}. M{quake['mag']:.1f} on {date_str} near {quake['place_en']} ({depth_str})\n"

        # Add note about partial results
        if is_partial:
            summary += f"\n\nNOTE: These are partial results. Processing was paused or stopped before completion."
            summary += f"\nPress ESC again to choose whether to continue or stop processing."

        self.summary_text.setText(summary)

    def update_quake_list(self, is_partial=False):
        """Update the earthquake list table"""
        self.quake_table.setRowCount(0)  # Clear existing rows

        # Sort events by magnitude (descending)
        sorted_events = sorted(self.events, key=lambda e: e["mag"], reverse=True)

        # Add rows to table
        for i, e in enumerate(sorted_events):
            self.quake_table.insertRow(i)

            # Format date/time
            date_time = e["time_local"].strftime("%Y-%m-%d %H:%M:%S")

            # Format depth
            depth_str = f"{e['depth']:.1f}" if e["depth"] is not None else "N/A"

            # Add items to row
            self.quake_table.setItem(i, 0, QTableWidgetItem(date_time))
            self.quake_table.setItem(i, 1, QTableWidgetItem(f"{e['mag']:.1f}"))
            self.quake_table.setItem(i, 2, QTableWidgetItem(depth_str))
            self.quake_table.setItem(i, 3, QTableWidgetItem(e["place_en"]))
            self.quake_table.setItem(i, 4, QTableWidgetItem(f"{e['lat']:.4f}"))
            self.quake_table.setItem(i, 5, QTableWidgetItem(f"{e['lon']:.4f}"))
            self.quake_table.setItem(i, 6, QTableWidgetItem(e.get("id", "")))

        # Update the tab title to indicate partial results
        if is_partial:
            self.results_tabs.setTabText(1, "Earthquake List (Partial)")

            # Add a note at the bottom of the table
            row_count = self.quake_table.rowCount()
            self.quake_table.insertRow(row_count)
            note_item = QTableWidgetItem("NOTE: These are partial results. Processing was paused or stopped.")
            note_item.setBackground(Qt.yellow)
            self.quake_table.setItem(row_count, 0, note_item)
            self.quake_table.setSpan(row_count, 0, 1, 7)  # Span across all columns
        else:
            self.results_tabs.setTabText(1, "Earthquake List")

    def generate_output_files(self):
        """Generate output files (CSV, KML, plots) based on checkbox settings"""
        self.update_status("Generating output files...")

        # Generate timestamp for filenames
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        source_label = "EMSC"

        # Generate files based on checkbox settings
        files_generated = []

        # Generate CSV file if selected
        if self.csv_checkbox.isChecked():
            self.generate_csv_file(timestamp, source_label)
            files_generated.append("CSV")

        # Generate KML file if selected
        if self.kmz_checkbox.isChecked():
            self.generate_kml_file(timestamp, source_label)
            files_generated.append("KMZ")

        # Generate plot files if selected
        if self.daily_counts_checkbox.isChecked() or self.magnitude_freq_checkbox.isChecked():
            self.generate_plot_files(timestamp, source_label)
            if self.daily_counts_checkbox.isChecked():
                files_generated.append("Daily Counts Plot")
            if self.magnitude_freq_checkbox.isChecked():
                files_generated.append("Magnitude Frequency Plot")

        # Generate summary report if selected
        if self.summary_checkbox.isChecked():
            self.generate_summary_report(timestamp, source_label)
            files_generated.append("Summary Report")

        if files_generated:
            self.update_status(f"Generated: {', '.join(files_generated)}")
        else:
            self.update_status("No output files were selected for generation.")

    def generate_csv_file(self, timestamp, source_label):
        """Generate CSV file with earthquake data"""
        csv_name = f"NearMandalay_{source_label}_{timestamp}.csv"
        with open(csv_name, "w", newline="", encoding="utf-8") as fh:
            w = csv.writer(fh)
            w.writerow(["local_time", "magnitude", "latitude", "longitude", "depth_km", "place_mm", "place_en", "source", "id"])
            for e in self.events:
                depth_str = f"{e['depth']:.1f}" if e["depth"] is not None else "N/A"
                w.writerow([
                    e["time_local"].isoformat(sep=" "),
                    e["mag"], e["lat"], e["lon"], depth_str,
                    e["place_mm"], e["place_en"],
                    e.get("source", ""),
                    e.get("id", "")
                ])

    def generate_kml_file(self, timestamp, source_label):
        """Generate KML file with earthquake data"""
        # Create KML object
        kml = simplekml.Kml()

        # Set KML document description with summary statistics
        kml.document.description = self.generate_kml_summary()

        # Define geographic boundaries
        min_longitude = 95.0
        max_longitude = 96.8
        min_latitude = 16.5
        max_latitude = 23.7
        search_radius = self.radius_spin.value()

        # Create a polygon for the bounding rectangle
        bounds = kml.newpolygon(
            name="Search Area",
            description="Earthquake search area from seismicportal.eu",
            outerboundaryis=[
                (min_longitude, min_latitude),
                (max_longitude, min_latitude),
                (max_longitude, max_latitude),
                (min_longitude, max_latitude),
                (min_longitude, min_latitude)
            ]
        )

        # Set polygon style - white outline, no fill, line width 3
        bounds.style.linestyle.color = simplekml.Color.white
        bounds.style.linestyle.width = 3
        bounds.style.polystyle.fill = 0  # No fill

        # Add search area visualization based on search method
        if self.search_method == "circle":
            # Add a circle centered at the focus city with radius search_radius
            circle_coords = self.create_circle_polygon(self.focus_lon, self.focus_lat, search_radius)
            circle = kml.newpolygon(
                name=f"Search Radius ({search_radius} km)",
                description=f"Circle with {search_radius} km radius around {self.focus_city}",
                outerboundaryis=circle_coords
            )

            # Set circle style - yellow outline, no fill, line width 2
            circle.style.linestyle.color = simplekml.Color.yellow
            circle.style.linestyle.width = 2
            circle.style.polystyle.fill = 0  # No fill

        # Add a marker at the focus city center
        city_point = kml.newpoint(
            name=self.focus_city,
            coords=[(self.focus_lon, self.focus_lat)],
            description=f"Center point for search"
        )
        # Use a different icon for the city center
        city_point.style.iconstyle.icon.href = "http://maps.google.com/mapfiles/kml/paddle/ylw-stars.png"
        city_point.style.iconstyle.scale = 1.2

        # Create folders for each magnitude range
        mag_folders = {}
        mag_ranges = [
            (3.0, 3.5), (3.5, 4.0), (4.0, 4.5), (4.5, 5.0), (5.0, 5.5),
            (5.5, 6.0), (6.0, 6.5), (6.5, 7.0), (7.0, 7.5), (7.5, 8.0)
        ]

        for low, high in mag_ranges:
            folder_name = f"M{low}-{high}"
            if high == 8.0:  # For the last range
                folder_name = f"M{low}+"
            mag_folders[(low, high)] = kml.newfolder(name=folder_name)

        # Add earthquake points to appropriate folders
        for e in self.events:
            # Determine which folder to use
            target_folder = None
            for (low, high), folder in mag_folders.items():
                if low <= e["mag"] < high or (high == 8.0 and e["mag"] >= low):
                    target_folder = folder
                    break

            # Create point in the appropriate folder
            depth_str = f"{e['depth']:.1f} km" if e["depth"] is not None else "Unknown depth"
            source = e.get("source", "")
            event_id = e.get("id", "")

            p = target_folder.newpoint(
                name=f"M{e['mag']:.1f} {e['place_mm']} {e['place_en']}",
                coords=[(e["lon"], e["lat"])],
                description=(
                    f"{e['time_local'].strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"Depth: {depth_str}\n"
                    f"{e['place_mm']} / {e['place_en']}\n"
                    f"Source: {source}\n"
                    f"ID: {event_id}"
                )
            )

            # Set icon and style based on magnitude
            p.style.iconstyle.icon.href = "http://maps.google.com/mapfiles/kml/shapes/earthquake.png"

            if e["mag"] < 5.0:
                p.style.iconstyle.color = simplekml.Color.orange
                p.style.iconstyle.scale = 0.6
                p.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/shaded_dot.png'
            elif e["mag"] < 5.5:
                p.style.iconstyle.color = simplekml.Color.yellow
                p.style.iconstyle.scale = 1.6
                p.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/donut.png'
            elif e["mag"] < 6.0:
                p.style.iconstyle.color = simplekml.Color.magenta
                p.style.iconstyle.scale = 1.8
                p.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/triangle.png'
            elif e["mag"] < 6.5:
                p.style.iconstyle.color = simplekml.Color.purple
                p.style.iconstyle.scale = 2.0
            elif e["mag"] < 7.0:
                p.style.iconstyle.color = simplekml.Color.darkmagenta
                p.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/star.png'
                p.style.iconstyle.scale = 2.2
            elif e["mag"] < 7.5:
                p.style.iconstyle.color = simplekml.Color.darkred
                p.style.iconstyle.scale = 2.4
            else:  # 7.5 and above
                p.style.iconstyle.color = simplekml.Color.red
                p.style.iconstyle.scale = 2.6

        # Create KMZ file
        kmz_name = f"NearMandalay_{source_label}_{timestamp}.kmz"
        with ZipFile(kmz_name, "w") as kmz:
            kmz.writestr("doc.kml", kml.kml().encode("utf-8"))

    def generate_kml_summary(self):
        """Generate summary text for KML description"""
        # Get current date and time for the KML creation timestamp
        current_date = datetime.datetime.now()
        date_formatted = current_date.strftime("%d-%B-%Y %H:%M:%S")

        # Format date range in a more readable format
        start_date = self.start_date_edit.date().toString("dd-MMMM-yyyy")
        end_date = self.end_date_edit.date().toString("dd-MMMM-yyyy")

        # Find largest earthquake
        largest_quake = max(self.events, key=lambda e: e["mag"])
        largest_quake_date = largest_quake["time_local"].strftime("%d-%B-%Y")
        largest_quake_place = largest_quake["place_en"]

        # Calculate average magnitude
        avg_magnitude = sum(e["mag"] for e in self.events) / len(self.events)

        # Count earthquakes by magnitude range
        mag_counts = defaultdict(int)

        # Collect earthquakes above 5.0 for detailed listing
        significant_quakes = []

        for e in self.events:
            mag = e["mag"]
            if mag >= 7.5:
                mag_counts["7.5+"] += 1
                significant_quakes.append(e)
            elif mag >= 7.0:
                mag_counts["7.0-7.5"] += 1
                significant_quakes.append(e)
            elif mag >= 6.5:
                mag_counts["6.5-7.0"] += 1
                significant_quakes.append(e)
            elif mag >= 6.0:
                mag_counts["6.0-6.5"] += 1
                significant_quakes.append(e)
            elif mag >= 5.5:
                mag_counts["5.5-6.0"] += 1
                significant_quakes.append(e)
            elif mag >= 5.0:
                mag_counts["5.0-5.5"] += 1
                significant_quakes.append(e)
            elif mag >= 4.5:
                mag_counts["4.5-5.0"] += 1
            elif mag >= 4.0:
                mag_counts["4.0-4.5"] += 1
            elif mag >= 3.5:
                mag_counts["3.5-4.0"] += 1
            elif mag >= 3.0:
                mag_counts["3.0-3.5"] += 1

        # Sort significant quakes by magnitude (descending)
        significant_quakes.sort(key=lambda e: e["mag"], reverse=True)

        # Format the summary text
        summary = f"""EARTHQUAKE SUMMARY
=================
Data source: EMSC
Date range: {start_date} to {end_date}
Total events: {len(self.events)} earthquakes

Largest: M{largest_quake['mag']:.1f} on {largest_quake_date} near {largest_quake_place}
Average magnitude: {avg_magnitude:.1f}

Magnitude distribution:
"""

        # Add magnitude distribution
        for mag_range, count in sorted(mag_counts.items(), reverse=True):
            if count > 0:  # Only include ranges with earthquakes
                summary += f"- {mag_range}: {count} earthquakes\n"

        # Add significant earthquakes (M5.0+) details
        if significant_quakes:
            summary += f"\nSignificant Earthquakes (M ≥ 5.0):\n"
            for i, quake in enumerate(significant_quakes, 1):
                depth_str = f"{quake['depth']:.1f} km" if quake["depth"] is not None else "Unknown depth"
                date_str = quake["time_local"].strftime("%Y-%m-%d")
                summary += f"{i}. M{quake['mag']:.1f} on {date_str} near {quake['place_en']} ({depth_str})\n"

        # Add KML creation timestamp
        summary += f"\nKML file created on {date_formatted}"

        return summary

    def create_circle_polygon(self, center_lon, center_lat, radius_km, num_points=64):
        """Create a circle approximation using a polygon with the specified number of points"""
        # Earth's radius in km
        earth_radius = 6371.0

        # Convert radius from km to radians
        radius_rad = radius_km / earth_radius

        # Generate points around the circle
        coords = []
        for i in range(num_points + 1):  # +1 to close the circle
            angle = 2 * math.pi * i / num_points
            # Calculate the point at the given angle and distance
            lat = math.asin(math.sin(math.radians(center_lat)) * math.cos(radius_rad) +
                            math.cos(math.radians(center_lat)) * math.sin(radius_rad) * math.cos(angle))
            lon = math.radians(center_lon) + math.atan2(math.sin(angle) * math.sin(radius_rad) * math.cos(math.radians(center_lat)),
                                                       math.cos(radius_rad) - math.sin(math.radians(center_lat)) * math.sin(lat))
            # Convert back to degrees
            lat = math.degrees(lat)
            lon = math.degrees(lon)
            coords.append((lon, lat))

        return coords

    def generate_plot_files(self, timestamp, source_label):
        """Generate plot files (magnitude frequency and time-based stacked bar chart)"""
        if not self.events:
            return

        # Define binning parameters
        bin_start = 3.5
        bin_end = 8.0
        bin_step = 0.5

        # Create bins
        bins = [round(bin_start + i*bin_step, 1) for i in range(int((bin_end - bin_start) / bin_step) + 1)]

        # Function to determine bin center for a magnitude
        def bin_center(m):
            return round(math.floor((m - bin_start) / bin_step) * bin_step + bin_start, 1)

        # Count frequencies
        counts = Counter(bin_center(e["mag"]) for e in self.events if e["mag"] >= bin_start)
        freqs = [counts.get(b, 0) for b in bins]

        # Generate colors
        colors = plt.cm.viridis_r(np.linspace(0.1, 0.9, len(bins)))

        # Format date range for display
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
        min_mag = float(self.mag_combo.currentText())

        # -- (a) frequency bar chart ---------------------------------------
        if self.magnitude_freq_checkbox.isChecked():
            plt.figure(figsize=(9, 4))
            bars = plt.bar([str(b) for b in bins], freqs, color=colors, edgecolor="none")
            plt.bar_label(bars, labels=[str(f) for f in freqs], padding=3, fontsize=9, color="black")
            plt.xlabel("Magnitude (0.5-unit M)")
            plt.ylabel("Count")

            plt.title(f"Frequency of earthquakes ≥ {min_mag} near Mandalay\n({start_date} – {end_date})")
            plt.tight_layout()

            # Save the plot as a PNG file
            magnitude_freq_filename = f"NearMandalay_{source_label}_magnitude_freq_{timestamp}.png"
            plt.savefig(magnitude_freq_filename, dpi=300)
            plt.close()

        # -- (b) time-based stacked bar chart ---------------------------------------
        if self.daily_counts_checkbox.isChecked():
            # Group data by day
            time_bins = defaultdict(lambda: Counter())
            for e in self.events:
                if e["mag"] < bin_start:
                    continue

                # Group by day
                time_key = e["time_local"].date()
                time_bins[time_key][bin_center(e["mag"])] += 1

            times_sorted = sorted(time_bins)

            # Create the stacked data
            stack_data = {b: [time_bins[t][b] for t in times_sorted] for b in bins}

            # Generate the plot
            plt.figure(figsize=(11, 5))
            bottom = np.zeros(len(times_sorted), dtype=int)

            for idx, b in enumerate(bins):
                vals = np.array(stack_data[b])
                plt.bar(times_sorted, vals, bottom=bottom,
                        color=colors[idx], edgecolor="none",
                        label=str(b), width=0.8)

                # per-segment count label
                for x, y0, v in zip(times_sorted, bottom, vals):
                    if v:
                        plt.text(x, y0 + v/2, str(v),
                                 ha='center', va='center', fontsize=7, color="black")

                bottom += vals

            # Format the plot
            plt.gca().xaxis.set_major_formatter(DateFormatter("%m-%d"))
            plt.xticks(rotation=45, ha='right')
            plt.ylabel("Daily count")
            plt.title(f"Daily earthquake counts by 0.5-magnitude range (≥ {min_mag})\n({start_date} – {end_date})")

            plt.legend(title="Magnitude range", fontsize="medium", title_fontsize="medium",
                       ncol=6, frameon=False)
            plt.tight_layout()

            # Save the plot
            daily_counts_filename = f"NearMandalay_{source_label}_daily_counts_{timestamp}.png"
            plt.savefig(daily_counts_filename, dpi=300)
            plt.close()

    def generate_summary_report(self, timestamp, source_label):
        """Generate summary report text file"""
        if not self.events:
            return

        # Get current date for the report
        current_date = datetime.datetime.now()
        day_name = current_date.strftime("%A")
        date_formatted = current_date.strftime("%d-%B-%Y %H:%M:%S")

        # Find largest earthquake
        largest_quake = max(self.events, key=lambda e: e["mag"])
        largest_quake_date = largest_quake["time_local"].strftime("%d-%B-%Y")
        largest_quake_place = largest_quake["place_en"]
        largest_quake_depth = f"{largest_quake['depth']:.1f} km" if largest_quake["depth"] is not None else "Unknown depth"

        # Calculate distance between largest quake and focus city
        distance_to_largest = self.haversine_km(
            self.focus_lat, self.focus_lon,
            largest_quake["lat"], largest_quake["lon"]
        )

        # Calculate average magnitude and depth
        avg_magnitude = sum(e["mag"] for e in self.events) / len(self.events)

        # Calculate average depth (excluding None values)
        depths = [e["depth"] for e in self.events if e["depth"] is not None]
        avg_depth = sum(depths) / len(depths) if depths else None

        # Find deepest earthquake
        deepest_quake = max([e for e in self.events if e["depth"] is not None], key=lambda e: e["depth"], default=None)
        deepest_info = ""
        if deepest_quake:
            deepest_info = f"Deepest earthquake: M{deepest_quake['mag']:.1f} at {deepest_quake['depth']:.1f} km near {deepest_quake['place_en']}"

        # Count earthquakes by magnitude range
        mag_counts = defaultdict(int)
        total_counts = {
            "above_4.5": 0,
            "above_4.0": 0,
            "above_3.5": 0,
            "above_3.0": 0,
            "total": len(self.events)
        }

        # Get quakes above 4.5 for detailed listing
        significant_quakes = []

        for e in self.events:
            mag = e["mag"]
            if mag >= 7.5:
                mag_counts["7.5+"] += 1
                total_counts["above_4.5"] += 1
                significant_quakes.append(e)
            elif mag >= 7.0:
                mag_counts["7.0-7.5"] += 1
                total_counts["above_4.5"] += 1
                significant_quakes.append(e)
            elif mag >= 6.5:
                mag_counts["6.5-7.0"] += 1
                total_counts["above_4.5"] += 1
                significant_quakes.append(e)
            elif mag >= 6.0:
                mag_counts["6.0-6.5"] += 1
                total_counts["above_4.5"] += 1
                significant_quakes.append(e)
            elif mag >= 5.5:
                mag_counts["5.5-6.0"] += 1
                total_counts["above_4.5"] += 1
                significant_quakes.append(e)
            elif mag >= 5.0:
                mag_counts["5.0-5.5"] += 1
                total_counts["above_4.5"] += 1
                significant_quakes.append(e)
            elif mag >= 4.5:
                mag_counts["4.5-5.0"] += 1
                total_counts["above_4.5"] += 1
                significant_quakes.append(e)
            elif mag >= 4.0:
                mag_counts["4.0-4.5"] += 1
                total_counts["above_4.0"] += 1
            elif mag >= 3.5:
                mag_counts["3.5-4.0"] += 1
                total_counts["above_3.5"] += 1
            elif mag >= 3.0:
                mag_counts["3.0-3.5"] += 1
                total_counts["above_3.0"] += 1
            elif mag >= 2.0:
                mag_counts["2.0-3.0"] += 1

        # Sort significant quakes by magnitude (descending)
        significant_quakes.sort(key=lambda e: e["mag"], reverse=True)

        # Create the report file
        report_filename = f"Earthquake_Summary_{source_label}_{timestamp}.txt"
        with open(report_filename, "w", encoding="utf-8") as f:
            f.write(f"EARTHQUAKE SUMMARY REPORT\n")
            f.write(f"(created by: eVolva Intelligent Solution)\n")
            f.write(f"=========================================\n\n")

            f.write(f"Data source: {source_label}\n")
            min_mag = float(self.mag_combo.currentText())
            search_radius = self.radius_spin.value()
            # Write search parameters based on search method
            if self.search_method == "circle":
                search_radius = self.radius_spin.value()
                f.write(f"Search parameters: Magnitude ≥ {min_mag}, Radius {search_radius} km around {self.focus_city}\n")
                f.write(f"Reference point: {self.focus_city} ({self.focus_lat:.4f}, {self.focus_lon:.4f})\n\n")
            else:
                min_lat = self.min_lat_spin.value()
                max_lat = self.max_lat_spin.value()
                min_lon = self.min_lon_spin.value()
                max_lon = self.max_lon_spin.value()
                f.write(f"Search parameters: Magnitude ≥ {min_mag}, Rectangle region\n")
                f.write(f"Latitude range: {min_lat:.4f} to {max_lat:.4f}\n")
                f.write(f"Longitude range: {min_lon:.4f} to {max_lon:.4f}\n")
                f.write(f"Reference point: {self.focus_city} ({self.focus_lat:.4f}, {self.focus_lon:.4f})\n\n")

            start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
            end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
            f.write(f"Start Time: {start_date}T00:00:00\n")
            f.write(f"End Time: {end_date}T23:59:59\n\n")

            f.write(f"Date report created: {day_name} {date_formatted}\n")
            f.write(f"Total events: {len(self.events)} earthquakes\n\n")

            f.write(f"Largest earthquake: M{largest_quake['mag']:.1f} on {largest_quake_date} near {largest_quake_place}\n")
            f.write(f"Depth of largest quake: {largest_quake_depth}\n")
            f.write(f"Distance between the largest quake and Mandalay: {distance_to_largest:.1f} km\n")
            f.write(f"Average magnitude: {avg_magnitude:.1f}\n")
            if avg_depth is not None:
                f.write(f"Average depth: {avg_depth:.1f} km\n")
            if deepest_quake:
                f.write(f"{deepest_info}\n")

            # Write total counts
            f.write(f"\nTotal {total_counts['total']} earthquakes above {min_mag}\n")
            f.write(f"Total {total_counts['above_4.5']} earthquakes above 4.5\n")
            f.write(f"Total {total_counts['above_4.0']} earthquakes above 4.0\n")
            f.write(f"Total {total_counts['above_3.5']} earthquakes above 3.5\n")
            f.write(f"Total {total_counts['above_3.0']} earthquakes above 3.0\n\n")

            # Write details of significant quakes (above 4.5)
            if significant_quakes:
                f.write("Significant Earthquakes (M ≥ 4.5)\n")
                f.write("================================\n")
                for i, quake in enumerate(significant_quakes, 1):
                    depth_str = f"{quake['depth']:.1f} km" if quake["depth"] is not None else "Unknown depth"
                    date_str = quake["time_local"].strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"{i}. M{quake['mag']:.1f} on {date_str}\n")
                    f.write(f"   Location: {quake['place_en']}\n")
                    if 'place_mm' in quake and quake['place_mm']:
                        f.write(f"   Local name: {quake['place_mm']}\n")
                    f.write(f"   Depth: {depth_str}\n")
                    f.write(f"   Coordinates: {quake['lat']:.4f}, {quake['lon']:.4f}\n")
                    f.write(f"   Source: {quake.get('source', '')}\n")
                    if 'id' in quake and quake['id']:
                        f.write(f"   ID: {quake['id']}\n\n")
                    else:
                        f.write("\n")
                f.write("\n")

            f.write("Quake list by Magnitude Range\n")
            f.write("============================\n")

            # Format the counts with appropriate wording
            for mag_range, count in sorted(mag_counts.items(), reverse=True):
                if count == 1:
                    descriptor = "One"
                elif count == 2:
                    descriptor = "Two"
                elif count == 3:
                    descriptor = "Three"
                else:
                    descriptor = str(count)

                if mag_range == "7.5+":
                    f.write(f"{descriptor} {mag_range} ({count})\n")
                elif mag_range == "7.0-7.5":
                    f.write(f"{descriptor} between 7.0 and 7.5 ({count})\n")
                elif mag_range == "6.5-7.0":
                    f.write(f"{descriptor} between 6.5 and 7.0 ({count})\n")
                elif mag_range == "6.0-6.5":
                    f.write(f"{descriptor} between 6.0 and 6.5 ({count})\n")
                elif mag_range == "5.5-6.0":
                    f.write(f"{descriptor} between 5.5 and 6.0 ({count})\n")
                elif mag_range == "5.0-5.5":
                    f.write(f"{descriptor} between 5.0 and 5.5 ({count})\n")
                elif mag_range == "4.5-5.0":
                    f.write(f"{descriptor} between 4.5 and 5.0 ({count})\n")
                elif mag_range == "4.0-4.5":
                    f.write(f"{descriptor} between 4.0 and 4.5 ({count})\n")
                elif mag_range == "3.5-4.0":
                    f.write(f"{descriptor} between 3.5 and 4.0 ({count})\n")
                elif mag_range == "3.0-3.5":
                    f.write(f"{descriptor} between 3.0 and 3.5 ({count})\n")
                elif mag_range == "2.0-3.0":
                    f.write(f"{descriptor} between 2.0 and 3.0 ({count})\n")

    def show_error(self, message):
        """Show error message"""
        QMessageBox.critical(self, "Error", message)

    def process_complete(self):
        """Clean up after processing is complete"""
        self.progress_bar.hide()

        # Only update UI if not stopped (stopped state is handled by on_worker_status_changed)
        if self.processing_state != "stopped":
            self.run_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.resume_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.processing_state = "idle"
            self.status_label.setText("Processing complete")

    def on_city_changed(self, city_name):
        """Handle city selection change"""
        if city_name in PREDEFINED_CITIES:
            self.focus_city = city_name
            self.focus_lat, self.focus_lon = PREDEFINED_CITIES[city_name]
            self.status_label.setText(f"Selected {city_name}: {self.focus_lat:.4f}, {self.focus_lon:.4f}")

    def on_search_method_changed(self):
        """Handle search method radio button changes"""
        if self.circle_radio.isChecked():
            self.search_method = "circle"
            self.circle_widget.show()
            self.rectangle_widget.hide()
        else:
            self.search_method = "rectangle"
            self.circle_widget.hide()
            self.rectangle_widget.show()

    def show_custom_coords_dialog(self):
        """Show dialog for entering custom coordinates"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Custom Coordinates")
        dialog.setMinimumWidth(300)

        layout = QVBoxLayout(dialog)

        # Create form for latitude and longitude
        form_layout = QFormLayout()

        # Latitude input
        lat_spin = QDoubleSpinBox()
        lat_spin.setRange(-90, 90)
        lat_spin.setDecimals(6)
        lat_spin.setValue(self.focus_lat)

        # Longitude input
        lon_spin = QDoubleSpinBox()
        lon_spin.setRange(-180, 180)
        lon_spin.setDecimals(6)
        lon_spin.setValue(self.focus_lon)

        # Add to form
        form_layout.addRow("Latitude:", lat_spin)
        form_layout.addRow("Longitude:", lon_spin)

        # Add form to layout
        layout.addLayout(form_layout)

        # Add location name field
        name_layout = QHBoxLayout()
        name_label = QLabel("Location Name:")
        name_edit = QLineEdit(self.focus_city)
        name_layout.addWidget(name_label)
        name_layout.addWidget(name_edit)
        layout.addLayout(name_layout)

        # Add buttons
        button_layout = QHBoxLayout()
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Cancel")

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        # Connect signals
        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)

        # Show dialog
        if dialog.exec_() == QDialog.Accepted:
            # Update focus city with custom coordinates
            self.focus_lat = lat_spin.value()
            self.focus_lon = lon_spin.value()
            self.focus_city = name_edit.text() if name_edit.text() else "Custom Location"

            # Add to combo box if not already present
            if self.city_combo.findText(self.focus_city) == -1:
                self.city_combo.addItem(self.focus_city)

            # Select the new city
            self.city_combo.setCurrentText(self.focus_city)

            # Update status
            self.status_label.setText(f"Custom location set: {self.focus_city} ({self.focus_lat:.6f}, {self.focus_lon:.6f})")

    def haversine_km(self, lat1, lon1, lat2, lon2):
        """Calculate distance between two points in km"""
        R = 6371.0
        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)
        a = math.sin(dlat/2)**2 + math.cos(math.radians(lat1))*math.cos(math.radians(lat2))*math.sin(dlon/2)**2
        return 2*R*math.asin(math.sqrt(a))


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = EarthquakeApp()
    window.show()
    sys.exit(app.exec_())