"""
near_mandalay_quakes.py
-------------------------------------------------------------
EMSC/SeismicPortal FDSN query → CSV + two plots
 - M ≥ 3.5
 - 2025-03-28 00:00 UTC  ➜  2025-05-18 23:59 UTC
 - within 400 km of Mandalay
 - Using EMSC data only (no USGS)
-------------------------------------------------------------
"""

import requests, math, datetime, csv, os
from collections import Counter, defaultdict
from zoneinfo import ZoneInfo
import matplotlib.pyplot as plt

# ---------------- USER SETTINGS ----------------
START_ISO = "2025-03-28T00:00:00"   # ← new start: 28-Mar-2025 00:00 UTC
END_ISO   = "2025-05-18T23:59:59"
MIN_MAG   = 3.5         #2.0
MAX_MAG   = 8.0
BIN_STEP  = 0.5
SEARCH_RADIUS_KM = 400

plotOption = 1  # 1 for day range
                # 2 for month range
                # 3 for year range
                # 0 for hour range


MANDALAY_LAT, MANDALAY_LON = 21.9759, 96.0845
LOCAL_TZ = ZoneInfo("Asia/Yangon")
# ----------------------------------------------

def haversine_km(lat1, lon1, lat2, lon2):
    R = 6371.0
    dlat = math.radians(lat2 - lat1)
    dlon = math.radians(lon2 - lon1)
    a = math.sin(dlat/2)**2 + math.cos(math.radians(lat1))*math.cos(math.radians(lat2))*math.sin(dlon/2)**2
    return 2*R*math.asin(math.sqrt(a))

# 1. ---- build URL and fetch data from EMSC -----------------

# Using EMSC as the only data source

# Define geographic boundaries
minlatitude = 16.5
maxlatitude = 23.7
minlongitude = 95.0
maxlongitude = 96.8
radius_deg = SEARCH_RADIUS_KM / 111.19

# Function to fetch and parse earthquake data from EMSC
def fetch_emsc_data():
    print("Fetching earthquake data from EMSC...")

    # Build EMSC URL with bounding box
    url = (
        "https://www.seismicportal.eu/fdsnws/event/1/query"
        "?format=json"
        f"&starttime={START_ISO}"
        f"&endtime={END_ISO}"
        f"&minlatitude={minlatitude}"
        f"&minlongitude={minlongitude}"
        f"&maxlatitude={maxlatitude}"
        f"&maxlongitude={maxlongitude}"
        f"&minmag={MIN_MAG}"
    )

    try:
        r = requests.get(url, timeout=60)
        r.raise_for_status()
        j = r.json()

        if "features" in j:            # GeoJSON
            src = j["features"]
            to_iso   = lambda ev: ev["properties"]["time"]
            to_mag   = lambda ev: ev["properties"]["mag"]
            to_lat   = lambda ev: ev["geometry"]["coordinates"][1]
            to_lon   = lambda ev: ev["geometry"]["coordinates"][0]
            to_depth = lambda ev: ev["geometry"]["coordinates"][2] if len(ev["geometry"]["coordinates"]) > 2 else None
            to_place = lambda ev: ev["properties"].get("flynn_region","")
        else:                           # flat list
            src = j.get("events", [])
            to_iso   = lambda e: e["time"]
            to_mag   = lambda e: e["mag"]
            to_lat   = lambda e: e["lat"]
            to_lon   = lambda e: e["lon"]
            to_depth = lambda e: e.get("depth", None)
            to_place = lambda e: e.get("flynn_region","")

        events = []
        for ev in src:
            mag = to_mag(ev)
            if mag is None or mag < MIN_MAG:  # Skip events without magnitude or below threshold
                continue

            lat, lon = to_lat(ev), to_lon(ev)
            if haversine_km(MANDALAY_LAT, MANDALAY_LON, lat, lon) > SEARCH_RADIUS_KM:
                continue

            t_utc = datetime.datetime.fromisoformat(to_iso(ev).replace("Z","+00:00"))
            t_loc = t_utc.astimezone(LOCAL_TZ)
            depth = to_depth(ev)

            # Convert depth to km if it's not None (some APIs provide depth in meters)
            if depth is not None and depth > 1000:
                depth = depth / 1000

            events.append({
                "time_local": t_loc,
                "mag": mag,
                "lat": lat,
                "lon": lon,
                "depth": depth,
                "place": to_place(ev),
                "source": "EMSC",
                "id": ev.get("id", "") if "id" in ev else ev.get("properties", {}).get("id", "")
            })

        print(f"Retrieved {len(events)} earthquakes from EMSC")
        return events

    except Exception as e:
        print(f"Error fetching EMSC data: {e}")
        return []

# Fetch earthquake data from EMSC
events = fetch_emsc_data()

if not events:
    print("No matching earthquakes.")
    raise SystemExit

# ------------------------------------------------------------------
# 3½.  REVERSE-GEOCODE  (mm + en with township fallback)
# ------------------------------------------------------------------
import time, unicodedata, re
from urllib.parse import urlencode
from collections import defaultdict

try:
    from unidecode import unidecode
    romanise = lambda s: unidecode(s)
except ImportError:
    romanise = lambda s: unicodedata.normalize("NFKD", s).encode("ascii","ignore").decode()

burmese_regex = re.compile(r"[\u1000-\u109F]")

cache = {}          # (lat,lon,lang)  ➜  address dict

def nominatim(lat, lon, lang):
    key = (round(lat,4), round(lon,4), lang)
    if key in cache:
        return cache[key]

    params = dict(format="jsonv2", lat=lat, lon=lon,
                  zoom=15, addressdetails=1,
                  accept_language=lang)
    url = "https://nominatim.openstreetmap.org/reverse?" + urlencode(params)
    headers = {"User-Agent": "Mandalay-Quake-Script/0.6 (contact: <EMAIL>)"}

    try:
        r = requests.get(url, headers=headers, timeout=10)
        data = r.json() if r.ok else {}
    except requests.RequestException:
        data = {}
    cache[key] = data
    time.sleep(1)      # respect 1 req/sec
    return data

priority_fine = ["village","quarter","municipality","county"]
priority_town = ["quarter","township","city", "town","county","district","state"]

print("Reverse-geocoding to Burmese & English (with fallback)…")
total_events = len(events)
for idx, e in enumerate(events, 1):
    print(f"\rProgress: {idx}/{total_events} locations processed...", end='', flush=True)

    mm_addr = nominatim(e["lat"], e["lon"], "my").get("address", {})
    en_addr = nominatim(e["lat"], e["lon"], "en").get("address", {})
    # if idx <= 5:  # Only print first 5 records
    #     print(f"\nRecord #{idx} en_addr content:")
    #     print(f"Latitude: {e['lat']}, Longitude: {e['lon']}")
    #     for key, value in en_addr.items():
    #         print(f"  {key}: {value}")
    # ---- Burmese name (fine level or fallback) -------------------
    for k in priority_fine + priority_town:
        if k in mm_addr:
            place_mm = mm_addr[k].strip()
            break
    else:
        place_mm = "unknown"

    # ---- English name prioritizing municipality and county over state ---------------------
    place_en = "Unknown"  # Default value

    # First try municipality directly
    if 'municipality' in en_addr and en_addr['municipality'].strip():
        place_en = en_addr['municipality'].strip()
        if 'county' in en_addr and en_addr['county'].strip():
            place_en += f", {en_addr['county']}"
    # Then try county if municipality not available
    elif 'county' in en_addr and en_addr['county'].strip():
        place_en = en_addr['county'].strip()
    # Then try district if neither municipality nor county available
    elif 'district' in en_addr and en_addr['district'].strip():
        place_en = en_addr['district'].strip()
    # Then try detailed location with municipality/county context
    else:
        for k in priority_fine:
            if k in en_addr and en_addr[k].strip():
                place_en = en_addr[k].strip()
                # Add municipality or county name for better context
                if 'municipality' in en_addr:
                    place_en += f", {en_addr['municipality']}"
                elif 'county' in en_addr:
                    place_en += f", {en_addr['county']}"
                break

    # If still no English name or if it contains Burmese characters, use state as fallback
    if place_en == "Unknown": #or burmese_regex.search(place_en):
        # Always end with state check
        if 'state' in en_addr:
            place_en = f"Near {en_addr['state']}"
        else:
            # Use romanized Burmese name as last resort
            place_en = romanise(place_mm)

    # Debug print to see what data we're getting
    # print(f"\nAddress data: {en_addr}")

    e["place_mm"] = place_mm
    e["place_en"] = place_en

print("\nReverse-geocoding completed!")


# ------------------------------------------------------------------
# 3.  SAVE CSV  (now with source and two place columns)
# ------------------------------------------------------------------
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

# Set the data source label for filenames
source_label = "EMSC"

csv_name = f"NearMandalay_{source_label}_{timestamp}.csv"
with open(csv_name, "w", newline="", encoding="utf-8") as fh:
    w = csv.writer(fh)
    w.writerow(["local_time","magnitude","latitude","longitude","depth_km","place_mm","place_en","source","id"])
    for e in events:
        depth_str = f"{e['depth']:.1f}" if e["depth"] is not None else "N/A"
        w.writerow([
            e["time_local"].isoformat(sep=" "),
            e["mag"], e["lat"], e["lon"], depth_str,
            e["place_mm"], e["place_en"],
            e.get("source", ""),  # Data source (USGS or EMSC)
            e.get("id", "")       # Event ID for reference
        ])
print("Data saved to", csv_name)

# ------------------------------------------------------------------
# 4.  WRITE KML → KMZ  (Google-hosted earthquake icon)
# ------------------------------------------------------------------
from zipfile import ZipFile
import simplekml

ICON_URL = "http://maps.google.com/mapfiles/kml/shapes/earthquake.png"

kml = simplekml.Kml()

# Generate summary statistics for KML description
def generate_kml_summary():
    # Get current date and time for the KML creation timestamp
    current_date = datetime.datetime.now()
    date_formatted = current_date.strftime("%d-%B-%Y %H:%M:%S")

    # Find largest earthquake
    largest_quake = max(events, key=lambda e: e["mag"])
    largest_quake_date = largest_quake["time_local"].strftime("%d-%B-%Y")
    largest_quake_place = largest_quake["place_en"]

    # Calculate average magnitude
    avg_magnitude = sum(e["mag"] for e in events) / len(events)

    # Count earthquakes by magnitude range
    mag_counts = defaultdict(int)
    for e in events:
        mag = e["mag"]
        if mag >= 7.5:
            mag_counts["7.5+"] += 1
        elif mag >= 7.0:
            mag_counts["7.0-7.5"] += 1
        elif mag >= 6.5:
            mag_counts["6.5-7.0"] += 1
        elif mag >= 6.0:
            mag_counts["6.0-6.5"] += 1
        elif mag >= 5.5:
            mag_counts["5.5-6.0"] += 1
        elif mag >= 5.0:
            mag_counts["5.0-5.5"] += 1
        elif mag >= 4.5:
            mag_counts["4.5-5.0"] += 1
        elif mag >= 4.0:
            mag_counts["4.0-4.5"] += 1
        elif mag >= 3.5:
            mag_counts["3.5-4.0"] += 1
        elif mag >= 3.0:
            mag_counts["3.0-3.5"] += 1

    # Format the summary text
    summary = f"""EARTHQUAKE SUMMARY
=================
Data source: {source_label}
Time period: {START_ISO} to {END_ISO}
Total events: {len(events)} earthquakes

Largest: M{largest_quake['mag']:.1f} on {largest_quake_date} near {largest_quake_place}
Average magnitude: {avg_magnitude:.1f}

Magnitude distribution:
"""

    # Add magnitude distribution
    for mag_range, count in sorted(mag_counts.items(), reverse=True):
        if count > 0:  # Only include ranges with earthquakes
            summary += f"- {mag_range}: {count} earthquakes\n"

    # Add KML creation timestamp
    summary += f"\nKML file created on {date_formatted}"

    return summary

# Set KML document description with summary statistics
kml.document.description = generate_kml_summary()


#Box 1
#
# Add bounding rectangle based on the seismicportal URL parameters
# Extract coordinates from the URL in line 47
min_longitude = 95  # 94.5
max_longitude = 96.8
min_latitude = 17.0 # 18.0
max_latitude = 24 # 23.7

# Create a polygon for the bounding rectangle
bounds = kml.newpolygon(
    name="Search Area",
    description="Earthquake search area from seismicportal.eu",
    outerboundaryis=[
        (min_longitude, min_latitude),
        (max_longitude, min_latitude),
        (max_longitude, max_latitude),
        (min_longitude, max_latitude),
        (min_longitude, min_latitude)
    ]
)

# Set polygon style - white outline, no fill, line width 3
bounds.style.linestyle.color = simplekml.Color.white
bounds.style.linestyle.width = 3
bounds.style.polystyle.fill = 0  # No fill

# Add a circle centered at Mandalay with radius SEARCH_RADIUS_KM
def create_circle_polygon(center_lon, center_lat, radius_km, num_points=64):
    """Create a circle approximation using a polygon with the specified number of points"""
    import math

    # Earth's radius in km
    earth_radius = 6371.0

    # Convert radius from km to radians
    radius_rad = radius_km / earth_radius

    # Generate points around the circle
    coords = []
    for i in range(num_points + 1):  # +1 to close the circle
        angle = 2 * math.pi * i / num_points
        # Calculate the point at the given angle and distance
        lat = math.asin(math.sin(math.radians(center_lat)) * math.cos(radius_rad) +
                        math.cos(math.radians(center_lat)) * math.sin(radius_rad) * math.cos(angle))
        lon = math.radians(center_lon) + math.atan2(math.sin(angle) * math.sin(radius_rad) * math.cos(math.radians(center_lat)),
                                                   math.cos(radius_rad) - math.sin(math.radians(center_lat)) * math.sin(lat))
        # Convert back to degrees
        lat = math.degrees(lat)
        lon = math.degrees(lon)
        coords.append((lon, lat))

    return coords

# Create the circle polygon
circle_coords = create_circle_polygon(MANDALAY_LON, MANDALAY_LAT, SEARCH_RADIUS_KM)
circle = kml.newpolygon(
    name=f"Search Radius ({SEARCH_RADIUS_KM} km)",
    description=f"Circle with {SEARCH_RADIUS_KM} km radius around Mandalay",
    outerboundaryis=circle_coords
)

# Set circle style - yellow outline, no fill, line width 2
circle.style.linestyle.color = simplekml.Color.yellow
circle.style.linestyle.width = 2
circle.style.polystyle.fill = 0  # No fill

# Add a marker at Mandalay city center
mandalay_point = kml.newpoint(
    name="Mandalay",
    coords=[(MANDALAY_LON, MANDALAY_LAT)],
    description=f"Center point for {SEARCH_RADIUS_KM} km radius search"
)
# Use a different icon for the city center
mandalay_point.style.iconstyle.icon.href = "http://maps.google.com/mapfiles/kml/paddle/ylw-stars.png"
mandalay_point.style.iconstyle.scale = 1.2

# Create folders for each magnitude range
mag_folders = {}
mag_ranges = [
    (3.0, 3.5), (3.5, 4.0), (4.0, 4.5), (4.5, 5.0), (5.0, 5.5),
    (5.5, 6.0), (6.0, 6.5), (6.5, 7.0), (7.0, 7.5), (7.5, 8.0)
]

for low, high in mag_ranges:
    folder_name = f"M{low}-{high}"
    if high == 8.0:  # For the last range
        folder_name = f"M{low}+"
    mag_folders[(low, high)] = kml.newfolder(name=folder_name)

for e in events:
    # Determine which folder to use
    target_folder = None
    for (low, high), folder in mag_folders.items():
        if low <= e["mag"] < high or (high == 8.0 and e["mag"] >= low):
            target_folder = folder
            break

    # Create point in the appropriate folder
    depth_str = f"{e['depth']:.1f} km" if e["depth"] is not None else "Unknown depth"
    source = e.get("source", "")
    event_id = e.get("id", "")

    p = target_folder.newpoint(
        name=f"M{e['mag']:.1f} {e['place_mm']} {e['place_en']}",
        coords=[(e["lon"], e["lat"])],
        description=(
            f"{e['time_local'].strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Depth: {depth_str}\n"
            f"{e['place_mm']} / {e['place_en']}\n"
            f"Source: {source}\n"
            f"ID: {event_id}"
        )
    )
    # use the Google-hosted PNG
    p.style.iconstyle.icon.href = ICON_URL

    # # magnitude-dependent colour tint and size
    # if e["mag"] < 3.0:
    #     p.style.iconstyle.color = simplekml.Color.lightblue
    #     p.style.iconstyle.scale = 0.6
    # elif e["mag"] < 3.5:
    #     p.style.iconstyle.color = simplekml.Color.green
    #     p.style.iconstyle.scale = 0.8
    # elif e["mag"] < 4.0:
    #     p.style.iconstyle.color = simplekml.Color.lightgreen
    #     p.style.iconstyle.scale = 1.0
    # elif e["mag"] < 4.5:
    #     p.style.iconstyle.color = simplekml.Color.yellow
    #     p.style.iconstyle.scale = 1.2
    # elif e["mag"] < 5.0:
    #     p.style.iconstyle.color = simplekml.Color.orange
    #     p.style.iconstyle.scale = 1.4
    # elif e["mag"] < 5.5:
    #     p.style.iconstyle.color = simplekml.Color.red
    #     p.style.iconstyle.scale = 1.6
    # elif e["mag"] < 6.0:
    #     p.style.iconstyle.color = simplekml.Color.magenta
    #     p.style.iconstyle.scale = 1.8
    # elif e["mag"] < 6.5:
    #     p.style.iconstyle.color = simplekml.Color.purple
    #     p.style.iconstyle.scale = 2.0
    # elif e["mag"] < 7.0:
    #     p.style.iconstyle.color = simplekml.Color.darkblue
    #     p.style.iconstyle.scale = 2.2
    # elif e["mag"] < 7.5:
    #     p.style.iconstyle.color = simplekml.Color.darkred
    #     p.style.iconstyle.scale = 2.4
    # else:  # 7.5 and above
    #     p.style.iconstyle.color = simplekml.Color.black
    #     p.style.iconstyle.scale = 2.6

    # magnitude-dependent colour tint and size

    if e["mag"] < 5.0:
        p.style.iconstyle.color = simplekml.Color.orange
        p.style.iconstyle.scale = 0.6
        p.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/shaded_dot.png'

    elif e["mag"] < 5.5:
        p.style.iconstyle.color = simplekml.Color.yellow
        p.style.iconstyle.scale = 1.6
        p.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/donut.png'
    elif e["mag"] < 6.0:
        p.style.iconstyle.color = simplekml.Color.magenta
        p.style.iconstyle.scale = 1.8
        'http://maps.google.com/mapfiles/kml/shapes/triangle.png'
    elif e["mag"] < 6.5:
        p.style.iconstyle.color = simplekml.Color.purple
        p.style.iconstyle.scale = 2.0
    elif e["mag"] < 7.0:
        p.style.iconstyle.color = simplekml.Color.darkmagenta
        p.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/star.png'
        p.style.iconstyle.scale = 2.2
    elif e["mag"] < 7.5:
        p.style.iconstyle.color = simplekml.Color.darkred
        p.style.iconstyle.scale = 2.4
    else:  # 7.5 and above
        p.style.iconstyle.color = simplekml.Color.red
        p.style.iconstyle.scale = 2.6

# --- create KMZ with just the KML ---------------------------------
kmz_name = f"NearMandalay_{source_label}_{timestamp}.kmz"
with ZipFile(kmz_name, "w") as kmz:
    kmz.writestr("doc.kml", kml.kml().encode("utf-8"))

print("KMZ saved to", kmz_name)



# ------------------------------------------------------------------
# 4½.  BINNING  (now 3.5 → 8.0, 0.5 step)
# ------------------------------------------------------------------
BIN_START = 3.5 # 3.0
BIN_END   = 8.0
BIN_STEP  = 0.5

def bin_center(m):
    # Shift so 3.50-3.99 → 3.5, 4.00-4.49 → 4.0, …
    return round(math.floor((m - BIN_START) / BIN_STEP) * BIN_STEP + BIN_START, 1)

bins = [round(BIN_START + i*BIN_STEP, 1)
        for i in range(int((BIN_END - BIN_START) / BIN_STEP) + 1)]

counts = Counter(bin_center(e["mag"]) for e in events if e["mag"] >= BIN_START)
freqs  = [counts.get(b, 0) for b in bins]

# ------------------------------------------------------------------
# 5.  TIME-BASED STACK DATA (hourly, daily, monthly, yearly)
# ------------------------------------------------------------------
# Check if we have enough data for the selected plot option
if plotOption == 0:  # hourly
    # Calculate time span of the data
    event_times = [e["time_local"] for e in events if e["mag"] >= BIN_START]
    if event_times:
        min_time = min(event_times)
        max_time = max(event_times)
        time_span_hours = (max_time - min_time).total_seconds() / 3600

        # If time span is less than 6 hours, hourly makes sense
        # Otherwise, we might want to fall back to daily
        if time_span_hours < 6:
            print(f"Time span is {time_span_hours:.1f} hours - using hourly bins")
        else:
            print(f"Warning: Time span is {time_span_hours:.1f} hours ({time_span_hours/24:.1f} days)")
            print("Continuing with hourly bins, but consider using daily (plotOption=1) for better visualization")

# Group data based on plotOption
time_bins = defaultdict(lambda: Counter())
for e in events:
    if e["mag"] < BIN_START:
        continue

    # Extract time component based on plotOption
    if plotOption == 0:  # hourly
        # For hourly, keep the datetime object with zeroed minutes/seconds
        # This ensures proper datetime sorting and formatting
        time_key = e["time_local"].replace(minute=0, second=0, microsecond=0)
    elif plotOption == 1:  # daily (default)
        time_key = e["time_local"].date()
    elif plotOption == 2:  # monthly
        time_key = e["time_local"].replace(day=1, hour=0, minute=0, second=0, microsecond=0).date()
    elif plotOption == 3:  # yearly
        time_key = e["time_local"].replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0).date()
    else:  # fallback to daily if invalid option
        time_key = e["time_local"].date()

    time_bins[time_key][bin_center(e["mag"])] += 1

times_sorted = sorted(time_bins)

# For hourly plots, fill in missing hours to ensure continuous timeline
if plotOption == 0 and len(times_sorted) > 1:
    # Create a complete set of hourly bins
    from datetime import timedelta

    # Get the first and last time
    start_time = times_sorted[0]
    end_time = times_sorted[-1]

    # Create a complete list of hourly bins
    complete_times = []
    current_time = start_time
    while current_time <= end_time:
        complete_times.append(current_time)
        current_time += timedelta(hours=1)

    # Check if we need to fill in missing bins
    if len(complete_times) > len(times_sorted):
        print(f"Filling in {len(complete_times) - len(times_sorted)} missing hourly bins")

        # Create a new time_bins dictionary with all hours
        filled_time_bins = defaultdict(lambda: Counter())

        # Copy existing data
        for t in times_sorted:
            for b in bins:
                filled_time_bins[t][b] = time_bins[t][b]

        # Use the filled bins
        times_sorted = complete_times
        time_bins = filled_time_bins

# Create the stacked data
stack_data = {b: [time_bins[t][b] for t in times_sorted] for b in bins}

# Debug information about the time bins
if plotOption == 0:  # hourly
    print(f"Created {len(times_sorted)} hourly bins")
    if len(times_sorted) > 0:
        print(f"First bin: {times_sorted[0].strftime('%Y-%m-%d %H:00')}")
        if len(times_sorted) > 1:
            print(f"Last bin: {times_sorted[-1].strftime('%Y-%m-%d %H:00')}")

            # Calculate time difference between first and last bin
            time_diff = times_sorted[-1] - times_sorted[0]
            hours_diff = time_diff.total_seconds() / 3600
            print(f"Time span: {hours_diff:.1f} hours ({time_diff.days} days, {time_diff.seconds//3600} hours)")

            # Check if we have the expected number of hourly bins
            expected_bins = int(hours_diff) + 1
            if len(times_sorted) != expected_bins:
                print(f"Warning: Expected {expected_bins} hourly bins, but got {len(times_sorted)}")
                print("This might indicate gaps in the data or issues with binning")

# ------------------------------------------------------------------
# 6.  PLOTTING  (gradient colours, NO total label on top)
# ------------------------------------------------------------------
import matplotlib.pyplot as plt
from matplotlib.dates import DateFormatter
import numpy as np

#cmap   = plt.cm.Blues                      # choose any sequential cmap
colors = plt.cm.viridis_r(np.linspace(0.1, 0.9, len(bins)))   # reversed vibrant color gradient

# -- (a) frequency bar chart ---------------------------------------
plt.figure(figsize=(9,4))
bars = plt.bar([str(b) for b in bins], freqs, color=colors, edgecolor="none")
plt.bar_label(bars, labels=[str(f) for f in freqs], padding=3, fontsize=9, color="black")
plt.xlabel("Magnitude (0.5-unit M)")
plt.ylabel("Count")
# Parse dates from ISO format for display
start_date = datetime.datetime.fromisoformat(START_ISO.replace("Z", "+00:00")).strftime("%Y-%m-%d")
end_date = datetime.datetime.fromisoformat(END_ISO.replace("Z", "+00:00")).strftime("%Y-%m-%d")
plt.title(f"Frequency of earthquakes ≥ {MIN_MAG} near Mandalay\n({start_date} – {end_date})")
plt.tight_layout()
# Save the plot as a PNG file instead of displaying it
plt.savefig(f"NearMandalay_{source_label}_magnitude_freq_{timestamp}.png", dpi=300)
plt.close()

# -- (b) time-based stacked bar chart (no total-count label) ------------
plt.figure(figsize=(11,5))
bottom = np.zeros(len(times_sorted), dtype=int)

for idx, b in enumerate(bins):
    vals = np.array(stack_data[b])
    plt.bar(times_sorted, vals, bottom=bottom,
            color=colors[idx], edgecolor="none",
            label=str(b), width=0.8)

    # per-segment count label
    for x, y0, v in zip(times_sorted, bottom, vals):
        if v:
            plt.text(x, y0 + v/2, str(v),
                     ha='center', va='center', fontsize=7, color="black")

    bottom += vals

# Set x-axis formatter and labels based on plotOption
if plotOption == 0:  # hourly
    from matplotlib.dates import HourLocator, DateFormatter

    # For hourly plots, we need more specific formatting
    ax = plt.gca()

    # Format the x-axis to show date and hour
    ax.xaxis.set_major_formatter(DateFormatter("%m-%d %H:00"))

    # Set appropriate locators for hourly data
    # This will place ticks at appropriate intervals
    if len(times_sorted) > 24:
        # If we have more than a day's worth of data, show every 6 hours
        ax.xaxis.set_major_locator(HourLocator(interval=6))
    else:
        # Otherwise show every hour
        ax.xaxis.set_major_locator(HourLocator())

    plt.xticks(rotation=45, ha='right')
    plt.ylabel("Hourly count")
    plt.title(f"Hourly earthquake counts by 0.5-magnitude range (≥ {MIN_MAG})\n({start_date} – {end_date})")
    output_filename = f"NearMandalay_{source_label}_hourly_counts_{timestamp}.png"

elif plotOption == 2:  # monthly
    plt.gca().xaxis.set_major_formatter(DateFormatter("%Y-%m"))
    plt.xticks(rotation=45, ha='right')
    plt.ylabel("Monthly count")
    plt.title(f"Monthly earthquake counts by 0.5-magnitude range (≥ {MIN_MAG})\n({start_date} – {end_date})")
    output_filename = f"NearMandalay_{source_label}_monthly_counts_{timestamp}.png"

elif plotOption == 3:  # yearly
    plt.gca().xaxis.set_major_formatter(DateFormatter("%Y"))
    plt.xticks(rotation=0)
    plt.ylabel("Yearly count")
    plt.title(f"Yearly earthquake counts by 0.5-magnitude range (≥ {MIN_MAG})\n({start_date} – {end_date})")
    output_filename = f"NearMandalay_{source_label}_yearly_counts_{timestamp}.png"

else:  # daily (default)
    plt.gca().xaxis.set_major_formatter(DateFormatter("%m-%d"))
    plt.xticks(rotation=45, ha='right')
    plt.ylabel("Daily count")
    plt.title(f"Daily earthquake counts by 0.5-magnitude range (≥ {MIN_MAG})\n({start_date} – {end_date})")
    output_filename = f"NearMandalay_{source_label}_daily_counts_{timestamp}.png"

plt.legend(title="Magnitue and above range", fontsize="medium", title_fontsize="medium",
           ncol=6, frameon=False)        # ← larger legend
plt.tight_layout()
# Save the plot as a PNG file instead of displaying it
plt.savefig(output_filename, dpi=300)
plt.close()
magnitude_freq_filename = f"NearMandalay_{source_label}_magnitude_freq_{timestamp}.png"
print(f"Plots saved as PNG files: {magnitude_freq_filename} and {output_filename}")

# ------------------------------------------------------------------
# 5. EXPORT SUMMARY REPORT TEXT FILE
# ------------------------------------------------------------------
def export_summary_report():
    # Get current date for the report
    current_date = datetime.datetime.now()
    day_name = current_date.strftime("%A")
    date_formatted = current_date.strftime("%d-%B-%Y %H:%M:%S")

    # Find largest earthquake
    largest_quake = max(events, key=lambda e: e["mag"])
    largest_quake_date = largest_quake["time_local"].strftime("%d-%B-%Y")
    largest_quake_place = largest_quake["place_en"]
    largest_quake_depth = f"{largest_quake['depth']:.1f} km" if largest_quake["depth"] is not None else "Unknown depth"

    # Calculate distance between largest quake and city center
    distance_to_largest = haversine_km(
        MANDALAY_LAT, MANDALAY_LON,
        largest_quake["lat"], largest_quake["lon"]
    )

    # Calculate average magnitude and depth
    avg_magnitude = sum(e["mag"] for e in events) / len(events)

    # Calculate average depth (excluding None values)
    depths = [e["depth"] for e in events if e["depth"] is not None]
    avg_depth = sum(depths) / len(depths) if depths else None

    # Find deepest earthquake
    deepest_quake = max([e for e in events if e["depth"] is not None], key=lambda e: e["depth"], default=None)
    deepest_info = ""
    if deepest_quake:
        deepest_info = f"Shallowest earthquake: M{deepest_quake['mag']:.1f} at {deepest_quake['depth']:.1f} km near {deepest_quake['place_en']}"

    # Count earthquakes by magnitude range
    mag_counts = defaultdict(int)
    total_counts = {
        "above_4.5": 0,
        "above_4.0": 0,
        "above_3.5": 0,
        "above_3.0": 0,
        "total": len(events)
    }

    # Get quakes above 4.5 for detailed listing
    significant_quakes = []

    for e in events:
        mag = e["mag"]
        if mag >= 7.5:
            mag_counts["7.5+"] += 1
            total_counts["above_4.5"] += 1
            significant_quakes.append(e)
        elif mag >= 7.0:
            mag_counts["7.0-7.5"] += 1
            total_counts["above_4.5"] += 1
            significant_quakes.append(e)
        elif mag >= 6.5:
            mag_counts["6.5-7.0"] += 1
            total_counts["above_4.5"] += 1
            significant_quakes.append(e)
        elif mag >= 6.0:
            mag_counts["6.0-6.5"] += 1
            total_counts["above_4.5"] += 1
            significant_quakes.append(e)
        elif mag >= 5.5:
            mag_counts["5.5-6.0"] += 1
            total_counts["above_4.5"] += 1
            significant_quakes.append(e)
        elif mag >= 5.0:
            mag_counts["5.0-5.5"] += 1
            total_counts["above_4.5"] += 1
            significant_quakes.append(e)
        elif mag >= 4.5:
            mag_counts["4.5-5.0"] += 1
            total_counts["above_4.5"] += 1
            significant_quakes.append(e)
        elif mag >= 4.0:
            mag_counts["4.0-4.5"] += 1
            total_counts["above_4.0"] += 1
        elif mag >= 3.5:
            mag_counts["3.5-4.0"] += 1
            total_counts["above_3.5"] += 1
        elif mag >= 3.0:
            mag_counts["3.0-3.5"] += 1
            total_counts["above_3.0"] += 1
        elif mag >= 2.0:
            mag_counts["2.0-3.0"] += 1

    # Sort significant quakes by magnitude (descending)
    significant_quakes.sort(key=lambda e: e["mag"], reverse=True)


    # Create the report file
    report_filename = f"Earthquake_Summary_{source_label}_{timestamp}.txt"
    with open(report_filename, "w", encoding="utf-8") as f:
        f.write(f"EARTHQUAKE SUMMARY REPORT\n")
        f.write(f"========================\n\n")

        f.write(f"Data source: {source_label}\n")
        f.write(f"Earthquakes in bounding box: Lat [{minlatitude:.4f} to {maxlatitude:.4f}], Lon [{minlongitude:.4f} to {maxlongitude:.4f}]\n")
        f.write(f"Reference point: Mandalay ({MANDALAY_LAT:.4f}, {MANDALAY_LON:.4f})\n\n")

        f.write(f"Start Time: {START_ISO}\n")
        f.write(f"End Time: {END_ISO}\n\n")

        f.write(f"Date report created: {day_name} {date_formatted}\n")
        f.write(f"Total events: {len(events)} earthquakes\n\n")

        f.write(f"Largest earthquake {largest_quake['mag']} quake at {largest_quake_date} near {largest_quake_place}\n")
        f.write(f"Depth of largest quake: {largest_quake_depth}\n")
        f.write(f"Distance between the biggest quake and the city: {distance_to_largest:.1f} km\n")
        f.write(f"Average quake: {avg_magnitude:.1f}\n")
        if avg_depth is not None:
            f.write(f"Average depth: {avg_depth:.1f} km\n")
        if deepest_quake:
            f.write(f"{deepest_info}\n")

        # Write total counts
        f.write(f"Total {total_counts['total']} earthquakes above {MIN_MAG}\n")
        f.write(f"Total {total_counts['above_4.5']} earthquakes above 4.5\n")
        f.write(f"Total {total_counts['above_4.0']} earthquakes above 4.0\n")
        f.write(f"Total {total_counts['above_3.5']} earthquakes above 3.5\n")
        f.write(f"Total {total_counts['above_3.0']} earthquakes above 3.0\n\n")

        # Write details of significant quakes (above 4.5)
        if significant_quakes:
            f.write("Significant Earthquakes (M ≥ 4.5)\n")
            f.write("================================\n")
            for i, quake in enumerate(significant_quakes, 1):
                depth_str = f"{quake['depth']:.1f} km" if quake["depth"] is not None else "Unknown depth"
                date_str = quake["time_local"].strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"{i}. M{quake['mag']:.1f} on {date_str}\n")
                f.write(f"   Location: {quake['place_en']}\n")
                if 'place_mm' in quake and quake['place_mm']:
                    f.write(f"   Local name: {quake['place_mm']}\n")
                f.write(f"   Depth: {depth_str}\n")
                f.write(f"   Coordinates: {quake['lat']:.4f}, {quake['lon']:.4f}\n")
                f.write(f"   Source: {quake.get('source', '')}\n")
                if 'id' in quake and quake['id']:
                    f.write(f"   ID: {quake['id']}\n\n")
                else:
                    f.write("\n")
            f.write("\n")

        f.write("Quake list by Magnitude Range\n")
        f.write("============================\n")

        # Format the counts with appropriate wording
        for mag_range, count in sorted(mag_counts.items(), reverse=True):
            if count == 1:
                descriptor = "One"
            elif count == 2:
                descriptor = "Two"
            elif count == 3:
                descriptor = "Three"
            else:
                descriptor = str(count)

            if mag_range == "7.5+":
                f.write(f"{descriptor} {mag_range} ({count})\n")
            elif mag_range == "7.0-7.5":
                f.write(f"{descriptor} between 7.0 and 7.5 ({count})\n")
            elif mag_range == "6.5-7.0":
                f.write(f"{descriptor} between 6.5 and 7.0 ({count})\n")
            elif mag_range == "6.0-6.5":
                f.write(f"{descriptor} between 6.0 and 6.5 ({count})\n")
            elif mag_range == "5.5-6.0":
                f.write(f"{descriptor} between 5.5 and 6.0 ({count})\n")
            elif mag_range == "5.0-5.5":
                f.write(f"{descriptor} between 5.0 and 5.5 ({count})\n")
            elif mag_range == "4.5-5.0":
                f.write(f"{descriptor} between 4.5 and 5.0 ({count})\n")
            elif mag_range == "4.0-4.5":
                f.write(f"{descriptor} between 4.0 and 4.5 ({count})\n")
            elif mag_range == "3.5-4.0":
                f.write(f"{descriptor} between 3.5 and 4.0 ({count})\n")
            elif mag_range == "3.0-3.5":
                f.write(f"{descriptor} between 3.0 and 3.5 ({count})\n")
            elif mag_range == "2.0-3.0":
                f.write(f"{descriptor} between 2.0 and 3.0 ({count})\n")

    print(f"Summary report saved to {report_filename}")

# Call the function to generate the report
export_summary_report()
