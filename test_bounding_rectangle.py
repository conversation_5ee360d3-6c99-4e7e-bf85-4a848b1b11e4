"""
Test script to verify the bounding rectangle in KML
"""

import simplekml

# Create KML
kml = simplekml.Kml()

# Add bounding rectangle based on the seismicportal URL parameters
min_longitude = 94.5
max_longitude = 96.8
min_latitude = 18.0
max_latitude = 23.7

# Create a polygon for the bounding rectangle
bounds = kml.newpolygon(
    name="Search Area",
    description="Earthquake search area from seismicportal.eu",
    outerboundaryis=[
        (min_longitude, min_latitude),
        (max_longitude, min_latitude),
        (max_longitude, max_latitude),
        (min_longitude, max_latitude),
        (min_longitude, min_latitude)
    ]
)

# Set polygon style - white outline, no fill, line width 3
bounds.style.linestyle.color = simplekml.Color.white
bounds.style.linestyle.width = 3
bounds.style.polystyle.fill = 0  # No fill

# Add a center point for reference
center_lat = (min_latitude + max_latitude) / 2
center_lon = (min_longitude + max_longitude) / 2
center_point = kml.newpoint(
    name="Center of Search Area",
    coords=[(center_lon, center_lat)],
    description="Center point of the earthquake search area"
)

# Save KML file
kml.save("test_bounding_rectangle.kml")
print("KML file saved as test_bounding_rectangle.kml")
print(f"Bounding rectangle coordinates: {min_longitude},{min_latitude} to {max_longitude},{max_latitude}")
print(f"Center point: {center_lon},{center_lat}")
