"""
Test script to verify the depth addition to the earthquake data
"""

import datetime
import csv
import simplekml
from zipfile import ZipFile

# Create sample earthquake data with depth
events = [
    {
        "time_local": datetime.datetime.now(),
        "mag": 3.2,
        "lat": 21.9,
        "lon": 96.1,
        "depth": 10.5,
        "place_mm": "Test MM 3.2",
        "place_en": "Test EN 3.2"
    },
    {
        "time_local": datetime.datetime.now(),
        "mag": 3.7,
        "lat": 22.0,
        "lon": 96.2,
        "depth": 15.3,
        "place_mm": "Test MM 3.7",
        "place_en": "Test EN 3.7"
    },
    {
        "time_local": datetime.datetime.now(),
        "mag": 4.2,
        "lat": 22.1,
        "lon": 96.3,
        "depth": None,  # Test with None depth
        "place_mm": "Test MM 4.2",
        "place_en": "Test EN 4.2"
    },
    {
        "time_local": datetime.datetime.now(),
        "mag": 5.1,
        "lat": 22.2,
        "lon": 96.4,
        "depth": 25.7,
        "place_mm": "Test MM 5.1",
        "place_en": "Test EN 5.1"
    },
]

# Test CSV output
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
csv_name = f"Test_Depth_{timestamp}.csv"
with open(csv_name, "w", newline="", encoding="utf-8") as fh:
    w = csv.writer(fh)
    w.writerow(["local_time", "magnitude", "latitude", "longitude", "depth_km", "place_mm", "place_en"])
    for e in events:
        depth_str = f"{e['depth']:.1f}" if e["depth"] is not None else "N/A"
        w.writerow([
            e["time_local"].isoformat(sep=" "),
            e["mag"], e["lat"], e["lon"], depth_str,
            e["place_mm"], e["place_en"]
        ])
print(f"CSV saved to {csv_name}")

# Test KML output
kml = simplekml.Kml()

# Create a folder for the test points
folder = kml.newfolder(name="Test Earthquakes")

for e in events:
    depth_str = f"{e['depth']:.1f} km" if e["depth"] is not None else "Unknown depth"
    p = folder.newpoint(
        name=f"M{e['mag']:.1f} {e['place_mm']} {e['place_en']}",
        coords=[(e["lon"], e["lat"])],
        description=(
            f"{e['time_local'].strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Depth: {depth_str}\n"
            f"{e['place_mm']} / {e['place_en']}"
        )
    )
    # Use a simple icon
    p.style.iconstyle.icon.href = "http://maps.google.com/mapfiles/kml/shapes/earthquake.png"

# Save KML file
kml_name = f"Test_Depth_{timestamp}.kml"
kml.save(kml_name)
print(f"KML saved to {kml_name}")

# Create KMZ file
kmz_name = f"Test_Depth_{timestamp}.kmz"
with ZipFile(kmz_name, "w") as kmz:
    kmz.writestr("doc.kml", kml.kml().encode("utf-8"))
print(f"KMZ saved to {kmz_name}")

# Test summary report
print("\nSummary Report Test:")
print("====================")

# Find largest earthquake
largest_quake = max(events, key=lambda e: e["mag"])
largest_quake_date = largest_quake["time_local"].strftime("%d-%B-%Y")
largest_quake_place = largest_quake["place_en"]
largest_quake_depth = f"{largest_quake['depth']:.1f} km" if largest_quake["depth"] is not None else "Unknown depth"

print(f"Largest earthquake {largest_quake['mag']} quake at {largest_quake_date} near {largest_quake_place}")
print(f"Depth of largest quake: {largest_quake_depth}")

# Calculate average magnitude and depth
avg_magnitude = sum(e["mag"] for e in events) / len(events)
print(f"Average magnitude: {avg_magnitude:.1f}")

# Calculate average depth (excluding None values)
depths = [e["depth"] for e in events if e["depth"] is not None]
avg_depth = sum(depths) / len(depths) if depths else None
if avg_depth is not None:
    print(f"Average depth: {avg_depth:.1f} km")

# Find deepest earthquake
deepest_quake = max([e for e in events if e["depth"] is not None], key=lambda e: e["depth"], default=None)
if deepest_quake:
    deepest_info = f"Deepest earthquake: M{deepest_quake['mag']:.1f} at {deepest_quake['depth']:.1f} km near {deepest_quake['place_en']}"
    print(deepest_info)

print(f"Total {len(events)} earthquakes")
