"""
Test script to verify the circle and center point in KML
"""

import simplekml
import math

# Constants from the main script
MANDALAY_LAT, MANDALAY_LON = 21.9759, 96.0845
SEARCH_RADIUS_KM = 400

# Create KML
kml = simplekml.Kml()

# Function to create a circle polygon
def create_circle_polygon(center_lon, center_lat, radius_km, num_points=64):
    """Create a circle approximation using a polygon with the specified number of points"""
    # Earth's radius in km
    earth_radius = 6371.0
    
    # Convert radius from km to radians
    radius_rad = radius_km / earth_radius
    
    # Generate points around the circle
    coords = []
    for i in range(num_points + 1):  # +1 to close the circle
        angle = 2 * math.pi * i / num_points
        # Calculate the point at the given angle and distance
        lat = math.asin(math.sin(math.radians(center_lat)) * math.cos(radius_rad) + 
                        math.cos(math.radians(center_lat)) * math.sin(radius_rad) * math.cos(angle))
        lon = math.radians(center_lon) + math.atan2(math.sin(angle) * math.sin(radius_rad) * math.cos(math.radians(center_lat)),
                                                   math.cos(radius_rad) - math.sin(math.radians(center_lat)) * math.sin(lat))
        # Convert back to degrees
        lat = math.degrees(lat)
        lon = math.degrees(lon)
        coords.append((lon, lat))
    
    return coords

# Create the circle polygon
circle_coords = create_circle_polygon(MANDALAY_LON, MANDALAY_LAT, SEARCH_RADIUS_KM)
circle = kml.newpolygon(
    name=f"Search Radius ({SEARCH_RADIUS_KM} km)",
    description=f"Circle with {SEARCH_RADIUS_KM} km radius around Mandalay",
    outerboundaryis=circle_coords
)

# Set circle style - yellow outline, no fill, line width 2
circle.style.linestyle.color = simplekml.Color.yellow
circle.style.linestyle.width = 2
circle.style.polystyle.fill = 0  # No fill

# Add a marker at Mandalay city center
mandalay_point = kml.newpoint(
    name="Mandalay",
    coords=[(MANDALAY_LON, MANDALAY_LAT)],
    description=f"Center point for {SEARCH_RADIUS_KM} km radius search"
)
# Use a different icon for the city center
mandalay_point.style.iconstyle.icon.href = "http://maps.google.com/mapfiles/kml/paddle/ylw-stars.png"
mandalay_point.style.iconstyle.scale = 1.2

# Add the bounding rectangle for comparison
min_longitude = 94.5
max_longitude = 96.8
min_latitude = 18.0
max_latitude = 23.7

# Create a polygon for the bounding rectangle
bounds = kml.newpolygon(
    name="Search Area",
    description="Earthquake search area from seismicportal.eu",
    outerboundaryis=[
        (min_longitude, min_latitude),
        (max_longitude, min_latitude),
        (max_longitude, max_latitude),
        (min_longitude, max_latitude),
        (min_longitude, min_latitude)
    ]
)

# Set polygon style - white outline, no fill, line width 3
bounds.style.linestyle.color = simplekml.Color.white
bounds.style.linestyle.width = 3
bounds.style.polystyle.fill = 0  # No fill

# Save KML file
kml.save("test_circle.kml")
print("KML file saved as test_circle.kml")
print(f"Circle center: {MANDALAY_LON},{MANDALAY_LAT}")
print(f"Circle radius: {SEARCH_RADIUS_KM} km")
print(f"Number of points in circle: {len(circle_coords)}")
print(f"Bounding rectangle coordinates: {min_longitude},{min_latitude} to {max_longitude},{max_latitude}")
