import simplekml
from math import radians, cos, sin, asin, sqrt, atan2, degrees, pi

def create_concentric_circles_kmz(output_filename="mandalay_circles_labeled.kmz"):
    """
    Creates a KMZ file with concentric circles centered at Mandalay, Myanmar,
    with fill colors, decreasing opacity, and labeled with red dots.

    Args:
        output_filename (str): The name of the output KMZ file.
    """

    # Coordinates for Mandalay, Myanmar
    mandalay_lon = 96.0771
    mandalay_lat = 21.9563

    # Radii of the concentric circles in kilometers
    radii_km = range(100, 900, 100)
    num_circles = len(radii_km)

    # Create a new KML object
    kml = simplekml.Kml()

    # Function to calculate points on a circle given a center, radius, and bearing
    def get_point_on_circle(lat, lon, radius_km, bearing_degrees):
        R = 6371  # Earth's radius in km
        bearing_radians = radians(bearing_degrees)
        lat_rad = radians(lat)
        lon_rad = radians(lon)

        new_lat_rad = asin(sin(lat_rad) * cos(radius_km / R) +
                           cos(lat_rad) * sin(radius_km / R) * cos(bearing_radians))

        new_lon_rad = lon_rad + atan2(sin(bearing_radians) * sin(radius_km / R) * cos(lat_rad),
                                     cos(radius_km / R) - sin(lat_rad) * sin(new_lat_rad))

        return (degrees(new_lon_rad), degrees(new_lat_rad))

    # Function to create the coordinates for a circle
    def create_circle_coords(center_lat, center_lon, radius_km, num_points=360):
        coordinates = []
        for i in range(num_points):
            bearing = i * (360 / num_points)
            lon, lat = get_point_on_circle(center_lat, center_lon, radius_km, bearing)
            coordinates.append((lon, lat))
        return coordinates

    # Create circles with varying colors, decreasing opacity fill, and labels with icons
    for i, radius in enumerate(radii_km):
        # Calculate line color (strongest at shortest, lightest at longest)
        base_line_color = "ff0000ff"  # Red
        alpha_line = int(255 * (1 - i / (num_circles - 1))) if num_circles > 1 else 255
        hex_alpha_line = "{:02x}".format(alpha_line)
        line_color = hex_alpha_line + base_line_color[2:]

        # Calculate fill color with decreasing opacity
        base_fill_color = "ff0000ff"  # Red
        opacity_percent = 36 - (i * 6)
        if opacity_percent < 5:
            opacity_percent = 5  # Ensure a small minimum opacity

        alpha_fill = int((opacity_percent / 100) * 255)
        hex_alpha_fill = "{:02x}".format(alpha_fill)
        fill_color = hex_alpha_fill + base_fill_color[2:]

        # Create the circle coordinates
        circle_coords = create_circle_coords(mandalay_lat, mandalay_lon, radius)

        # Create a polygon for the circle
        pol = kml.newpolygon(name=f"{radius} km Radius")
        pol.outerboundaryis = circle_coords
        pol.style.linestyle.color = line_color
        pol.style.linestyle.width = 2
        pol.style.polystyle.color = fill_color  # Set the fill color
        pol.style.polystyle.fill = 1  # Enable filling

        # Add text label with a red dot icon
        label_radius_km = radii_km[i]
        label_bearing = i * 10  + 90 # Start at 0 degrees (right) and increment clockwise
        label_bearing = 90 # Start at 0 degrees (right) and increment clockwise
        label_lon, label_lat = get_point_on_circle(mandalay_lat, mandalay_lon, radius, label_bearing)

        pnt = kml.newpoint(name=f"{label_radius_km}km", coords=[(label_lon, label_lat)])
        pnt.style.labelstyle.scale = 1.2  # Adjust label size as needed

        # Style for the red dot icon
        icon_size = 2.0 - (i * 0.1)
        if icon_size < 0.5:
            icon_size = 0.5  # Ensure a minimum icon size

        pnt.style.iconstyle.icon.href = 'http://maps.google.com/mapfiles/kml/shapes/placemark_circle.png'
        pnt.style.iconstyle.color = 'ff0000ff'  # Red color for the dot
        pnt.style.iconstyle.scale = icon_size

    # Save the KML as a KMZ file
    kml.savekmz(output_filename)

if __name__ == "__main__":
    create_concentric_circles_kmz()
    print("KMZ file 'mandalay_circles_labeled.kmz' created successfully with filled concentric circles and labeled with red dots around Mandalay.")